﻿@page "/UnlicensedAccess"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [AllowAnonymous]
@using Microsoft.Extensions.Localization
@using MudBlazor
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject NavigationManager NavigationManager

<MudContainer Class="d-flex justify-center align-center" Style="min-height: 100vh;">
    <MudPaper Elevation="4" Class="p-8" Style="max-width: 450px; text-align: center; border-radius: 16px;">
        <MudAvatar Size="Size.Large" Class="mx-auto mb-4" Style="background: linear-gradient(135deg, #a18cd1, #fbc2eb);">
            <MudIcon Icon="@Icons.Material.Filled.RocketLaunch" Color="Color.Primary" />
        </MudAvatar>

        <MudText Typo="Typo.h5" Class="font-weight-bold mb-2">
            @Localizer["Your trial is over"]
        </MudText>

        <MudText Typo="Typo.body2" Color="Color.Secondary" Class="mb-4">
            @Localizer["Your organization does not have an active license to use this software. Please subscribe or contact support."]
        </MudText>

        <MudStack Spacing="2" Class="mb-4" Style="text-align: left;">
            <MudStack Direction="Row" AlignItems="AlignItems.Center" Spacing="1">
                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                <MudText Typo="Typo.body2">@Localizer["Unlimited encounters"]</MudText>
            </MudStack>
            <MudStack Direction="Row" AlignItems="AlignItems.Center" Spacing="1">
                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                <MudText Typo="Typo.body2">@Localizer["Template customization, summaries, etc."]</MudText>
            </MudStack>
            <MudStack Direction="Row" AlignItems="AlignItems.Center" Spacing="1">
                <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" />
                <MudText Typo="Typo.body2">@Localizer["Cancel anytime"]</MudText>
            </MudStack>
        </MudStack>

        <MudButton OnClick="NavigateToContact" Variant="Variant.Filled" Color="Color.Primary" FullWidth="true" Class="mb-2">
            @Localizer["Contact Support"]
        </MudButton>

        <MudButton OnClick="@(() => NavigationManager.NavigateTo("/authentication/logout", true))"
                   Variant="Variant.Outlined"
                   Color="Color.Default"
                   FullWidth="true">
            @Localizer["Log out"]
        </MudButton>
    </MudPaper>
</MudContainer>
