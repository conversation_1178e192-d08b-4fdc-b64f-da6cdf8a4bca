﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Graph.Models;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class SocialHistory : ComponentBase
    {
        [Inject] public ISocialHistoryService SocialHistoryService { get; set; }
        
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private List<PatientSocialHistory> SocialHistoryList { get; set; }
        private List<PatientSocialHistory> AddList = new();
        private List<PatientSocialHistory> DeleteList = new();
        public SfGrid<PatientSocialHistory> SocialHistoryGrid { get; set; }
        public SfRichTextEditor RichTextEditor { get; set; }
        private MudDialog _addMemberDialog;
        private Guid PatientId { get; set; }
        private Guid? orgid { get; set; }
        private string editorContent;
        private bool add = false;
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Social History" },
            new ToolbarItemModel() { Name = "close" }
        };

        /// <summary>
        /// Initializes the component asynchronously and loads patient social history data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            orgid = OrgId;
            ManualContent = Data;

            Subscription = UserContext.ActiveUserSubscription;
            SocialHistoryList = await SocialHistoryService.GetAllByIdAndIsActiveAsync(PatientId, orgid, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        /// <summary>
        /// Loads social history data asynchronously for the given patient.
        /// </summary>
        private async Task LoadDataAsync()
        {
            
            SocialHistoryList = await SocialHistoryService.GetAllByIdAndIsActiveAsync(PatientId, orgid, Subscription);
            
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Opens the add task dialog for adding new social history records.
        /// </summary>
        private async Task OpenAddTaskDialog()
        {
            await _addMemberDialog.ShowAsync();
        }

        /// <summary>
        /// Handles the completion of actions such as Add, Save, and Delete in the social history grid.
        /// </summary>
        /// <param name="args">Action event arguments containing the details of the operation.</param>
        private void ActionCompletedHandler(ActionEventArgs<PatientSocialHistory> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedhistories = args.Data as PatientSocialHistory;
                    var existingItem = AddList.FirstOrDefault(existinghistory => existinghistory.SocialHistoryId == deletedhistories.SocialHistoryId);
                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedhistories.isActive = false;
                        deletedhistories.UpdatedBy = Guid.Parse(User.id);
                        deletedhistories.UpdatedDate = DateTime.Now;
                        DeleteList.Add(deletedhistories);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.SocialHistoryId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = orgid;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedhistories = args.Data;
                        if (addedhistories != null)
                        {
                            AddList.Add(addedhistories);
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<PatientSocialHistory> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
        }

        /// <summary>
        /// Saves the changes made to the social history records by adding new entries and updating existing ones.
        /// </summary>
        private async Task SaveChanges()
        {
            await SocialHistoryService.AddHistoryListAsync(AddList, orgid, Subscription);
            await SocialHistoryService.UpdateHistoryListAsync(DeleteList, orgid, Subscription);
            await SocialHistoryService.UpdateHistoryListAsync(SocialHistoryList, orgid, Subscription);
            AddList.Clear();
            DeleteList.Clear();
            await LoadDataAsync();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await _addMemberDialog.CloseAsync();
        }

        /// <summary>
        /// Cancels changes and reloads the original social history data.
        /// </summary>
        private async Task CancelChanges()
        {
            AddList.Clear();
            DeleteList.Clear();
            await LoadDataAsync();
            await _addMemberDialog.CloseAsync();
        }

        private string GenerateRichTextContent(string manualData)
        {

            string dynamicContent = SocialHistoryList != null
                ? string.Join(" ",
                    SocialHistoryList
                        .Where(s => !string.IsNullOrEmpty(s.LifestyleHabits))
                        .OrderByDescending(s => s.CreatedDate)
                        .Select(s => $"<ul><li style='margin-left: 20px;'><b>{s.CreatedDate:yyyy-MM-dd}</b> - {s.LifestyleHabits}</li></ul>"))
                : string.Empty;

            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }





        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}
