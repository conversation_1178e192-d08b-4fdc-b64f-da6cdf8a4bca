using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class License
    {
        private string? errorMessage = null;
        private string? username;
        private const int ten = 10;
        private Organization Organization { get; set; } = new Organization();
        private UserLicense UserLicense { get; set; } = new UserLicense();
        private List<Organization> Organizations = new();
        private List<PlanType> PlanTypes = new();
        private List<Product> Products = new();
        private List<Organization> OrganizationList = new();
        private List<PlanType> PlanList = new();
        private List<Product> ProductList = new();
        private List<UserLicense> UserLicenses = new();
        private List<Organization> ActiveOrganizations = new();
        private List<UserLicense> ActiveUserLicenses = new();
        private SfGrid<UserLicense>? licenseGrid;
        public string[] ToolBarItems = new string[] { "Edit", "Delete", "Cancel" };

        [Inject] private ActiveUser User { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        [Inject] private ILogger<License> Logger { get; set; }

        /// <summary>
        /// Initializes the component and loads the necessary data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                await LoadMembersAsync();
                await GetActiveOrganizationsList();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error : {ex.Message}");
            }
        }

        /// <summary>
        /// Loads members data asynchronously.
        /// </summary>
        private async Task LoadMembersAsync()
        {
            try
            {
                username = User.id;
                Organizations = await OrganizationService.GetAllOrganizationsAsync();
                PlanTypes = (await PlanTypeService.GetAllPlanTypesAsync()).ToList();
                Products = (await ProductService.GetProductsAsync()).ToList();

                OrganizationList = Organizations.Select(o => new Organization { OrganizationId = o.OrganizationId, OrganizationName = o.OrganizationName }).ToList();
                PlanList = PlanTypes.Select(p => new PlanType { Id = p.Id, PlanName = p.PlanName }).ToList();
                ProductList = Products.Select(pr => new Product { Id = pr.Id, Name = pr.Name }).ToList();

                UserLicenses = (await UserLicenseService.GetAllUserLicensesAsync()).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading organizations: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the list of active organizations asynchronously.
        /// </summary>
        private async Task GetActiveOrganizationsList()
        {
            try
            {
                errorMessage = null;
                ActiveOrganizations = Organizations.Where(org => org.IsActive).ToList();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                errorMessage = string.Format(Localizer["ErrorFetchingOrganizations"], ex.Message);
            }
        }

        private Guid _selectedOrganization;
        public Guid selectedOrganization
        {
            get => _selectedOrganization;
            set
            {
                _selectedOrganization = value;
                _ = OnOrganizationChanged(value);
            }
        }

        /// <summary>
        /// Handles the event when the selected organization is changed.
        /// </summary>
        /// <param name="selectedOrganization">The selected organization ID.</param>
        private async Task OnOrganizationChanged(Guid selectedOrganization)
        {
            if (selectedOrganization == Guid.Empty) return;

            try
            {
                Logger.LogInformation(Localizer["FetchingLicenses"], selectedOrganization);
                var license = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(selectedOrganization);

                ActiveUserLicenses.Clear();

                if (license != null)
                {
                    ActiveUserLicenses.Add(license);
                    Logger.LogInformation(Localizer["FetchedOneLicense"], selectedOrganization);
                }
                else
                {
                    Logger.LogWarning(Localizer["NoLicenseFound"], selectedOrganization);
                    var freeLicense = await AddFreeUserLicense(selectedOrganization);
                    ActiveUserLicenses.Add(freeLicense);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorAddingLicense"]);
                ActiveUserLicenses.Clear();
            }
            finally
            {
                if (licenseGrid != null)
                {
                    await licenseGrid.Refresh();
                }
                StateHasChanged();
            }
        }

        /// <summary>
        /// Adds a free user license for the specified organization.
        /// </summary>
        /// <param name="selectedOrganization">The selected organization ID.</param>
        private async Task<UserLicense?> AddFreeUserLicense(Guid selectedOrganization)
        {
            UserLicense? result = null;

            try
            {
                if (selectedOrganization != Guid.Empty)
                {
                    var freePlanId = PlanTypes.FirstOrDefault(p => p.PlanName == "Free")?.Id ?? Guid.Empty;

                    var freeUserLicense = new UserLicense
                    {
                        Id = Guid.NewGuid(),
                        PlanId = freePlanId,
                        OrganizationId = selectedOrganization,
                        Seats = ten,
                        CreatedDate = DateTime.UtcNow,
                        CreatedBy = Guid.Parse(username),
                        Status = true,
                        ExpiryDate = DateTime.UtcNow.AddDays(14)
                    };

                    await UserLicenseService.AddUserLicenseAsync(freeUserLicense);
                    Logger.LogInformation(Localizer["FreeLicenseAdded"], selectedOrganization);
                    await RefreshActiveUserLicenses();
                    result = freeUserLicense;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorAddingLicense"]);
            }

            return result;
        }

        /// <summary>
        /// Refreshes the list of active user licenses.
        /// </summary>
        private async Task RefreshActiveUserLicenses()
        {
            try
            {
                ActiveUserLicenses.Clear();
                ActiveUserLicenses.Add(await UserLicenseService.GetUserLicenseByOrganizationIdAsync(selectedOrganization));
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error refreshing active user licenses: {ex.Message}");
            }
            finally
            {
                if (licenseGrid != null)
                {
                    await licenseGrid.Refresh();
                }
                StateHasChanged();
            }
        }

        /// <summary>
        /// Handles the action begin event for the grid.
        /// </summary>
        /// <param name="args">The action event arguments.</param>
        private async Task OnActionBegin(ActionEventArgs<UserLicense> args)
        {
            try
            {
                Logger.LogInformation(Localizer["ActionBeginTriggered"], args.RequestType);

                if (args.RequestType == Syncfusion.Blazor.Grids.Action.BeginEdit)
                {
                    Logger.LogInformation(Localizer["EditingUserLicense"], args.RowData?.Seats);
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    args.Cancel = true;

                    var result = await ShowSaveConfirmationDialog(args.Data);
                    if (result)
                    {
                        Logger.LogInformation(Localizer["UpdatingUserLicense"], args.Data?.Seats);
                        await UpdateUserLicense(args.Data);
                    }
                    else
                    {
                        await RefreshGrid();
                    }
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    args.Cancel = true;

                    var result = await ShowDeleteConfirmationDialog(args.Data);
                    if (result)
                    {
                        args.Data.Status = false;
                        Logger.LogInformation(Localizer["DeletingUserLicense"], args.Data?.Seats);
                        await UpdateUserLicense(args.Data);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorInOnActionBegin"]);
            }
        }

        /// <summary>
        /// Shows a save confirmation dialog.
        /// </summary>
        /// <param name="license">The user license being saved.</param>
        /// <returns>True if user confirms, false otherwise.</returns>
        private async Task<bool> ShowSaveConfirmationDialog(UserLicense license)
        {
            bool result = false;

            try
            {
                bool? confirmed = await DialogService.ShowMessageBox(
                    Localizer["ConfirmSaveTitle"],
                    Localizer["ConfirmSaveMessage"],
                    yesText: Localizer["Save"],
                    cancelText: Localizer["Cancel"]);

                result = confirmed == true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorShowingSaveDialog"]);
            }

            return result;
        }

        /// <summary>
        /// Shows a delete confirmation dialog.
        /// </summary>
        /// <param name="license">The user license being deleted.</param>
        /// <returns>True if user confirms, false otherwise.</returns>
        private async Task<bool> ShowDeleteConfirmationDialog(UserLicense license)
        {
            bool result = false;

            try
            {
                bool? confirmed = await DialogService.ShowMessageBox(
                    Localizer["ConfirmDeleteTitle"],
                    Localizer["ConfirmDeleteMessage"],
                    yesText: Localizer["Delete"],
                    cancelText: Localizer["Cancel"]);

                result = confirmed == true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorShowingDeleteDialog"]);
            }

            return result;
        }


        /// <summary>
        /// Handles the action complete event for the grid.
        /// </summary>
        /// <param name="args">The action event arguments.</param>
        private void OnActionComplete(ActionEventArgs<UserLicense> args)
        {
            Logger.LogInformation(Localizer["ActionCompleteTriggered"], args.RequestType);
        }

        /// <summary>
        /// Updates the user license.
        /// </summary>
        /// <param name="license">The user license to update.</param>
        private async Task UpdateUserLicense(UserLicense license)
        {
            try
            {
                await UserLicenseService.UpdateUserLicenseAsync(license);
                await RefreshGrid();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error updating user license: {ex.Message}");
            }
        }

        /// <summary>
        /// Refreshes the grid.
        /// </summary>
        private async Task RefreshGrid()
        {
            if (licenseGrid != null)
            {
                await licenseGrid.Refresh();
            }
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Gets the display name of the user.
        /// </summary>
        /// <param name="userId">The user ID.</param>
        /// <returns>The display name of the user.</returns>
        private string GetUserDisplayName(Guid? userId)
        {
            return User.mail.ToString();
        }
    }
}