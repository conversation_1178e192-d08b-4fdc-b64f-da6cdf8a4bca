﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class CurrentMedicationGrid : ComponentBase
    {
        [Parameter] public Guid ChiefComplaintId { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }
        private List<ChiefComplaintDTO> chiefComplaints = new();
        [Inject] public IRxNormService RxNormService { get; set; }
        [Inject] public ICurrentMedicationService CurrentMedicationService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IMemberService _MemberService { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IChiefComplaintService ChiefComplaintService { get; set; }

        protected List<string> BrandNames { get; set; } = new List<string>();
        public List<string> BrandSBD { get; set; } = new List<string>();
        public string drugName { get; set; }
        public string finalSBD { get; set; }
        private string _Quantity;
        private string _Frequency;

        public SfGrid<ActiveMedication> MedicinesGrid { get; set; }
        private Guid PatientID { get; set; }
        private Guid? OrgID { get; set; }
        private List<ActiveMedication> deleteList { get; set; } = new List<ActiveMedication>();
        private List<ActiveMedication> AddList = new();
        private List<ActiveMedication> medications { get; set; }
        private List<ActiveMedication> medicationRelatedToAssessments { get; set; }
        private List<ActiveMedication> medicationRelatedToAssessments1 { get; set; }
        public List<ChiefComplaintDTO> LocalData { get; private set; } = new();
        private List<string> chiefComplaintDescriptions = new List<string>();
        private string CheifComplaintName;
        [Parameter] public EventCallback OnChange { get; set; }

        private void NotifyParentOfChanges()
        {
            OnChange.InvokeAsync();
        }

        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            medications = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            medicationRelatedToAssessments = medications
            .Where(med => med.CheifComplaintId == ChiefComplaintId)
            .ToList();
            LocalData = (await ChiefComplaintService.GetByPatientIdAsync(PatientID, OrgID, Subscription))
            .GroupBy(c => c.Description)
            .Select(g => g.OrderByDescending(c => c.DateOfComplaint).First())
            .ToList();
            SharedNotesService.OnChange += UpdateComplaints;
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            CheifComplaintName = LocalData
             .FirstOrDefault(c => c.Id == ChiefComplaintId)?
             .Description ?? string.Empty;
            BrandNames = await RxNormService.GetAllBrandNames();
            medications = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            medicationRelatedToAssessments = medications
            .Where(med => med.CheifComplaintId == ChiefComplaintId)
            .ToList();
        }
       
        private RenderFragment<object> ChiefComplaintEditTemplate => (context) => (builder) =>
        {
            if (context is not ActiveMedication medication) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", chiefComplaintDescriptions);
            builder.AddAttribute(2, "Value", medication.CheifComplaint);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    medication.CheifComplaint = value;
                    var selectedComplaint = LocalData.FirstOrDefault(c => c.Description == value);
                    if (selectedComplaint != null)
                    {
                        medication.CheifComplaintId = selectedComplaint.Id;
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Chief Complaint");
            builder.CloseComponent();
        };

        private void UpdateComplaints()
        {
            chiefComplaints = SharedNotesService.GetChiefComplaints();
            chiefComplaintDescriptions = LocalData.Select(c => c.Description).ToList();
            InvokeAsync(StateHasChanged);
        }


        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;

            if (!string.IsNullOrEmpty(value))
            {
                BrandSBD = await RxNormService.GetSBDNamesAsync(value);
                finalSBD = null;
                StateHasChanged();
            }
            else
            {
                BrandSBD.Clear();
                finalSBD = null;
                StateHasChanged();
            }
        }

        protected Task<IEnumerable<string>> SearchBrandNames(string value, CancellationToken cancellationToken)
        {
            IEnumerable<string> result;

            if (cancellationToken.IsCancellationRequested)
            {
                result = Enumerable.Empty<string>();
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                result = BrandNames.AsEnumerable();
            }
            else
            {
                result = BrandNames.Where(b => b.Contains(value, StringComparison.OrdinalIgnoreCase)).AsEnumerable();
            }

            return Task.FromResult(result);
        }

        private async Task AddNewMedication()
        {
            if (string.IsNullOrEmpty(drugName) || string.IsNullOrEmpty(finalSBD))
            {
                Snackbar.Add(@Localizer["Please fill in both Brand Name and Drug Details fields"], Severity.Warning);
                return;
            }
            var newMedication = new ActiveMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = PatientID,
                PCPId = Guid.Parse(User.id),
                OrganizationId = _PatientService.PatientData.OrganizationID ?? Guid.Empty,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = PatientID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                BrandName = drugName,
                DrugDetails = finalSBD,
                Quantity = _Quantity ?? "Not Specified",
                Frequency = _Frequency ?? "Not Specified",
                isActive = true,
                StartDate = null,
                EndDate = null,
                CheifComplaint = CheifComplaintName,
                CheifComplaintId = ChiefComplaintId
            };

            AddList.Add(newMedication);
            medicationRelatedToAssessments.Add(newMedication);
            await MedicinesGrid.Refresh();
            ResetInputFields();
        }

        private void ResetInputFields()
        {
            drugName = string.Empty;
            finalSBD = null;
            BrandSBD.Clear();
            _Quantity = null;
            _Frequency = null;

        }

        public async Task ActionCompletedHandler(Syncfusion.Blazor.Grids.ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedMedication = args.Data;
                var existingItem = AddList.FirstOrDefault(v => v.MedicineId == deletedMedication.MedicineId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.isActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
            await Task.CompletedTask;
        }

        public async Task ActionBeginHandler(Syncfusion.Blazor.Grids.ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.StartDate.HasValue && args.Data.EndDate.HasValue &&
                    args.Data.StartDate > args.Data.EndDate)
                {
                    Snackbar.Add(@Localizer["Start Date cannot be after End Date"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
            await Task.CompletedTask;
        }

        public async Task SaveChanges()
        {
            if (AddList.Any(medication => string.IsNullOrWhiteSpace(medication.CheifComplaint)))
            {
                Snackbar.Add("Each medication must have a Chief Complaint.", Severity.Warning);
                return;
            }
            if (AddList.Count != 0)
            {
                await CurrentMedicationService.AddMedicationAsync(AddList, OrgID, Subscription);
            }
            await CurrentMedicationService.UpdateMedicationListAsync(deleteList, OrgID, Subscription);
            await CurrentMedicationService.UpdateMedicationListAsync(medicationRelatedToAssessments, OrgID, Subscription);
            deleteList.Clear();
            AddList.Clear();
            NotifyParentOfChanges();
            await InvokeAsync(StateHasChanged);
        }

        public async Task CancelChanges()
        {
            deleteList.Clear();
            AddList.Clear();
            medicationRelatedToAssessments = await CurrentMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            ResetInputFields();
            OnInitializedAsync();
            NotifyParentOfChanges();
            await MedicinesGrid.Refresh();
            await InvokeAsync(StateHasChanged);
        }
    }
}
