﻿using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using static MudBlazor.Icons.Custom;
using Unity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Syncfusion.Blazor;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Text;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Immunization
    {
        [Inject] public IVaccineService VaccineService { get; set; }
        [Inject] private ICustomImmunizationAlertService CustomImmunizationAlertService { get; set; }
        [Inject] public IAlertService AlertService { get; set; }
        [Inject] public IImmunizationService _ImmunizationService { get; set; }
        [Inject] private ILogger<Immunization> _logger { get; set; }
        [Inject] private IStringLocalizer<Immunization> _localizer { get; set; }

        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        //[Inject] private IOrganizationService OrganizationService { get; set; }
        //[Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private IMemberService MemberService { get; set; }

        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private MudDialog _immunization;
        private SfGrid<ImmunizationData> ImmunizationGrid;

        private List<ImmunizationData> immunization { get; set; }
        private List<Alert> pendingAlerts = new List<Alert>();
        private Vaccines SelectedVaccine { get; set; }
        public string VaccineName { get; set; }

        public string SelectedCPTCode { get; set; }
        public string SelectedCVXCode { get; set; }
        public string SelectedCPTDescription { get; set; }
        public DateTime GivenDate { get; set; } = DateTime.Now;
        public string Comments { get; set; }

        private SfRichTextEditor RichTextEditor;

        private static readonly char[] SplitChars = { ' ', ',', '-', '(', ')', '/' };
        private Patient _PatientData = new Patient();
        private List<Vaccines> _details { get; set; } = new List<Vaccines>();

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid Id { get; set; }

        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }

        private string? ManualContent { get; set; }

        private List<FDBVaccines> FDBVaccineList { get; set; } = new List<FDBVaccines>();
        private FDBVaccines FDBSelectedVaccine { get; set; }
        public enum Source { CDC, FDB }

        private string selectedSource = Source.CDC.ToString();

        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" },
            new ToolbarItemModel() { Name = "close" },
        };

        [Inject]
        private PatientService _PatientService { get; set; }
        private Guid PatientId { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        private List<ImmunizationData> deletesurgerylist { get; set; } = new List<ImmunizationData>();
        private List<ImmunizationData> AddList = new();
        private int patientAge;
        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            ManualContent = Data;
            PatientId = PatientID;
            OrgID = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            var existingImmunizations = await _ImmunizationService.GetImmunizationByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            immunization = existingImmunizations?.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList() ?? new List<ImmunizationData>();

            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
            // Start Phase 2 in background without awaiting
            _ = LoadImmunizationDataAsync();
        }

        private async Task LoadImmunizationDataAsync()
        {

            // Phase 2: Load additional medication data
            var patient = await MemberService.GetMemberByIdAsync(PatientId, OrgID ?? Guid.Empty, false);
            _PatientData.DOB = patient.DateOfBirth;
            _PatientData.Sex = patient.SexualOrientation;
            _PatientData.Name = patient.FirstName;


            if (_PatientData.DOB.HasValue)
            {
                patientAge = (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25);
            }
            else
            {
                patientAge = 0; // Set to 0 if DOB is not available
            }
            int emptyRowsNeeded = 9 - immunization.Count;
            if (emptyRowsNeeded > 0)
            {
                immunization.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new ImmunizationData
                    {
                        Immunizations = string.Empty,
                        CPTCode = string.Empty,
                        CVXCode = string.Empty,
                        Comments = string.Empty
                    }));
            }
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        private async Task OpenNewDialogBox()
        {
            await _immunization.ShowAsync();
        }

        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _immunization.CloseAsync();
        }

        private CancellationTokenSource _searchVaccineCancellationTokenSource;
        private CancellationTokenSource _searchFDBVaccineCancellationTokenSource;
        protected async Task<IEnumerable<Vaccines>> SearchVaccinesData(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<Vaccines>();

            // Cancel previous search if still running
            _searchVaccineCancellationTokenSource?.Cancel();
            _searchVaccineCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine cancellation tokens
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchVaccineCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with search term
                var results = await VaccineService.GetAllVaccinesDataBySearchTermAsync(searchTerm);

                // Store the results in _details for later use
                _details = results
                    .Where(t => !string.IsNullOrEmpty(t.VaccineName))
                    .ToList();

                return _details;
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<Vaccines>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search vaccines");
                return Enumerable.Empty<Vaccines>();
            }
        }

        private async Task<IEnumerable<FDBVaccines>> SearchFDBVaccine(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<FDBVaccines>();

            // Cancel previous search if still running
            _searchFDBVaccineCancellationTokenSource?.Cancel();
            _searchFDBVaccineCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine cancellation tokens
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchFDBVaccineCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with search term
                return await FDBService.GetVaccinesBySearchTerm(searchTerm);
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<FDBVaccines>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search FDB vaccines");
                return Enumerable.Empty<FDBVaccines>();
            }
        }
        private async Task OnSourceChanged(string newSource)
        {
            selectedSource = newSource;

            // Reset all vaccine-related fields when source changes
            ResetInputFields();

            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(_localizer["BackdropDisabledMessage"], Severity.Info);
        }

        public void ActionCompletedHandler(ActionEventArgs<ImmunizationData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedImmunization = args.Data as ImmunizationData;
                var existingItem = AddList.FirstOrDefault(v => v.ImmunizationId == deletedImmunization.ImmunizationId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deletesurgerylist.Add(args.Data);
                }
            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<ImmunizationData> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.GivenDate > DateTime.Now.Date)
                {
                    Snackbar.Add(@Localizer["Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        private async Task<bool> CheckAgeAppropriateImmunizations()
        {
            bool hasAgeWarnings = false;
            List<(string Message, string SeverityLevel)> warningMessages = new List<(string, string)>();

            foreach (var immunization in AddList)
            {
                if (string.IsNullOrEmpty(immunization.Immunizations))
                    continue;

                var ageRecommendation = await GetVaccineAgeRecommendation(immunization.Immunizations);
                var isAgeAppropriate = IsVaccineAgeAppropriate(ageRecommendation, patientAge);

                if (!isAgeAppropriate)
                {
                    hasAgeWarnings = true;
                    string warningMessage = await GetDetailedAgeWarning(immunization.Immunizations, patientAge, ageRecommendation);

                    string severityLevel = "MEDIUM";
                    var severityMatch = System.Text.RegularExpressions.Regex.Match(
                        warningMessage,
                        @"Severity Level: (LOW|MEDIUM|HIGH)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    if (severityMatch.Success)
                    {
                        severityLevel = severityMatch.Groups[1].Value.ToUpper();
                    }

                    warningMessages.Add((warningMessage, severityLevel));
                }
            }

            foreach (var immunization in this.immunization.Where(i => !string.IsNullOrEmpty(i.Immunizations)))
            {
                if (string.IsNullOrEmpty(immunization.Immunizations))
                    continue;

                if (AddList.Any(a => a.ImmunizationId == immunization.ImmunizationId))
                    continue;

                var ageRecommendation = await GetVaccineAgeRecommendation(immunization.Immunizations);
                var isAgeAppropriate = IsVaccineAgeAppropriate(ageRecommendation, patientAge);

                if (!isAgeAppropriate)
                {
                    hasAgeWarnings = true;
                    string warningMessage = await GetDetailedAgeWarning(immunization.Immunizations, patientAge, ageRecommendation);

                    string severityLevel = "MEDIUM";
                    var severityMatch = System.Text.RegularExpressions.Regex.Match(
                        warningMessage,
                        @"Severity Level: (LOW|MEDIUM|HIGH)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    if (severityMatch.Success)
                    {
                        severityLevel = severityMatch.Groups[1].Value.ToUpper();
                    }

                    warningMessages.Add((warningMessage, severityLevel));
                }
            }


            return hasAgeWarnings;
        }

        private async Task SaveData()
        {

            bool? saveResult = await DialogService.ShowMessageBox(
                _localizer["Confirm Save"],
                _localizer["Are you sure you want to save these changes?"],
                yesText: _localizer["Yes"],
                noText: _localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }
            try
            {
                if (AddList.Any(item => item.GivenDate == null || item.GivenDate == DateTime.MinValue))
                {
                    Snackbar.Add(_localizer["Please select given date for all immunizations"], Severity.Warning);
                    return;
                }
                if (AddList.Count > 0)
                {
                    await _ImmunizationService.AddImmunizationAsync(AddList, OrgID, Subscription);
                }
                if (deletesurgerylist.Count > 0)
                {
                    await _ImmunizationService.UpdateImmunizationListAsync(deletesurgerylist, OrgID, Subscription);
                }

                if (pendingAlerts.Any())
                {
                    await AlertService.AddAlertsAsync(pendingAlerts, OrgID, false);
                    pendingAlerts.Clear();
                }



                await _ImmunizationService.UpdateImmunizationListAsync(immunization.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList(), OrgID, Subscription);

                deletesurgerylist.Clear();
                AddList.Clear();

                //UpdateEditorContent();
                editorContent = GenerateRichTextContent(ManualContent);
                await HandleDynamicComponentUpdate();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                ResetInputFields();

                await InvokeAsync(StateHasChanged);
                await CloseNewDialogBox();
                //await CheckImmunizationsForAlerts(alertsCheckList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving immunization data");
                Snackbar.Add(_localizer["Failed to save immunization records"], Severity.Error);
            }
        }
        private async Task<List<Alert>> CheckImmunizationsForAlerts(List<ImmunizationData> immunizationsToCheck)
        {
            if (immunizationsToCheck == null || !immunizationsToCheck.Any())
                return new List<Alert>(); // Return empty list

            int patientAge = _PatientData.DOB.HasValue
                ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25)
                : 0;
            string patientGender = _PatientData.Sex ?? "Unknown";
            var alertsToAdd = new List<Alert>();

            // Custom immunization alerts (optional - similar to custom lab alerts)
            var customImmunizationAlerts = await GetCustomImmunizationAlerts(immunizationsToCheck);

            if (customImmunizationAlerts.Count > 0)
            {
                foreach (var customAlert in customImmunizationAlerts)
                {
                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = PatientId,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = OrgID ?? Guid.Empty,
                        Severity = "Not Configured",
                        AlertType = "Configured Immunization Alert",
                        Description = customAlert.Description ?? $"Custom alert for {customAlert.Name}",
                        Solution = $"Follow the guidelines for {customAlert.Name}.",
                        AdditionalInfo = $"Age Range: {customAlert.AgeLowerBound}-{customAlert.AgeUpperBound}, Gender: {customAlert.Gender}",
                        CreatedDate = DateTime.Now,
                        OrdersetId = customAlert.OrdersetId,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    Snackbar.Add($"[CONFIGURED ALERT] {customAlert.Name} - {customAlert.Description}",
                                 Severity.Info,
                                 config => {
                                     config.VisibleStateDuration = 10000;
                                     config.Icon = Icons.Material.Filled.MedicalServices;
                                 });
                }
            }

            // Check each immunization for age-related alerts
            foreach (var immunization in immunizationsToCheck)
            {
                if (string.IsNullOrEmpty(immunization.Immunizations))
                    continue;

                // Get age recommendation for the vaccine
                var ageRecommendation = await GetVaccineAgeRecommendation(immunization.Immunizations);

                // Check if the vaccine is age-appropriate
                bool isAgeAppropriate = IsVaccineAgeAppropriate(ageRecommendation, patientAge);

                if (!isAgeAppropriate)
                {
                    string severityResponse = await GetImmunizationSeverity(immunization.Immunizations, patientAge, ageRecommendation);
                    string severityLevel = ExtractSeverityLevel(severityResponse);

                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = PatientId,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = OrgID ?? Guid.Empty,
                        Severity = severityLevel,
                        AlertType = "AI Suggested Immunization Alert",
                        Description = $"The immunization '{immunization.Immunizations}' may not be appropriate for the patient's age.",
                        Solution = $"Consult with a healthcare provider. {severityResponse}",
                        AdditionalInfo = $"Vaccine: {immunization.Immunizations}, " +
                                         $"Recommended Age: {ageRecommendation}, " +
                                         $"Patient Age: {patientAge}, " +
                                         $"Gender: {patientGender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    var snackbarSeverity = severityLevel switch
                    {
                        "High" => Severity.Error,
                        "Medium" => Severity.Warning,
                        "Low" => Severity.Info,
                        _ => Severity.Warning
                    };

                    Snackbar.Add($"[AI SUGGESTED ALERT] {immunization.Immunizations} may not be suitable for {patientAge}yr old. Severity: {severityLevel}",
                                 snackbarSeverity,
                                 config => {
                                     config.VisibleStateDuration = 10000;
                                     config.Icon = Icons.Material.Filled.Warning;
                                 });
                }
            }

            return alertsToAdd; // ✅ Return alerts instead of saving them here
        }


        private async Task<List<CustomImmunizationAlerts>> GetCustomImmunizationAlerts(List<ImmunizationData> immunizationsToCheck)
        {
            try
            {
                // Fetch custom immunization alerts for the patient
                var customImmunizationAlerts = await CustomImmunizationAlertService.GetActiveCustomImmunizationAlertsByOrganizationIdAsync(Id, OrgID, false);

                if (customImmunizationAlerts == null || customImmunizationAlerts.Count == 0)
                    return new List<CustomImmunizationAlerts>();

                // Calculate patient age and gender
                int patientAge = _PatientData.DOB.HasValue
                    ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25)
                    : 0;
                string patientGender = _PatientData.Sex ?? "Unknown";

                var matchingAlerts = new List<CustomImmunizationAlerts>();

                // Extract immunization names
                var immunizationNames = immunizationsToCheck
                    .Where(i => !string.IsNullOrEmpty(i.Immunizations))
                    .Select(i => i.Immunizations)
                    .Distinct()
                    .ToList();

                if (immunizationNames.Count == 0)
                    return matchingAlerts;

                // Prepare alerts description
                StringBuilder alertsDescription = new StringBuilder();
                for (int i = 0; i < customImmunizationAlerts.Count; i++)
                {
                    var alert = customImmunizationAlerts[i];
                    alertsDescription.AppendLine($"Alert {i + 1}:");
                    alertsDescription.AppendLine($"- Name: {alert.Name}");
                    alertsDescription.AppendLine($"- Description: {alert.Description}");
                    alertsDescription.AppendLine($"- Order Set: {alert.OrderSet}");
                    alertsDescription.AppendLine($"- Age Range: {(alert.AgeLowerBound.HasValue ? alert.AgeLowerBound.Value.ToString() : "Any")} to {(alert.AgeUpperBound.HasValue ? alert.AgeUpperBound.Value.ToString() : "Any")}");
                    alertsDescription.AppendLine($"- Gender: {alert.Gender ?? "Any"}");
                    alertsDescription.AppendLine();
                }

                // Prepare immunization info
                StringBuilder immunizationInfo = new StringBuilder();
                foreach (var name in immunizationNames)
                {
                    immunizationInfo.AppendLine($"- {name}");
                }

                // Prepare alerts summary
                StringBuilder alertsSummary = new StringBuilder();
                for (int i = 0; i < customImmunizationAlerts.Count; i++)
                {
                    var alert = customImmunizationAlerts[i];
                    alertsSummary.AppendLine($"Alert {i + 1}: {alert.Name} - {alert.Description}");
                }

                // Create GPT prompt
                string prompt = $"Determine which alerts apply to these immunizations:\n\n" +
                    $"Patient: {patientAge}yrs, {patientGender}\n" +
                    $"Immunizations:\n{immunizationInfo}\n\n" +
                    $"Alerts:\n{alertsSummary}\n" +
                    $"Return comma-separated alert numbers that apply (e.g., '1,3,5'). If none apply, return 'None'.";

                // Ask GPT
                string response = await AskGptModel(prompt);

                // Process GPT response for direct matches
                if (!response.Trim().Equals("None", StringComparison.OrdinalIgnoreCase))
                {
                    var matches = Regex.Matches(response, @"\d+");
                    foreach (Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int alertIndex) &&
                            alertIndex >= 1 &&
                            alertIndex <= customImmunizationAlerts.Count)
                        {
                            matchingAlerts.Add(customImmunizationAlerts[alertIndex - 1]);
                        }
                    }
                }

                // If no direct matches, do a more detailed search
                if (matchingAlerts.Count == 0)
                {
                    foreach (var alert in customImmunizationAlerts)
                    {
                        bool vaccineMatch = false;

                        // Check name matching
                        if (!string.IsNullOrEmpty(alert.Name))
                        {
                            foreach (var immunizationName in immunizationNames)
                            {
                                if (alert.Name.Contains(immunizationName, StringComparison.OrdinalIgnoreCase))
                                {
                                    vaccineMatch = true;
                                    break;
                                }
                            }
                        }

                        // Check description matching
                        if (!vaccineMatch && !string.IsNullOrEmpty(alert.Description))
                        {
                            foreach (var immunizationName in immunizationNames)
                            {
                                if (alert.Description.Contains(immunizationName, StringComparison.OrdinalIgnoreCase))
                                {
                                    vaccineMatch = true;
                                    break;
                                }
                            }
                        }

                        // Check vaccine set matching
                        if (!vaccineMatch && !string.IsNullOrEmpty(alert.OrderSet))
                        {
                            foreach (var immunizationName in immunizationNames)
                            {
                                if (alert.OrderSet.Contains(immunizationName, StringComparison.OrdinalIgnoreCase))
                                {
                                    vaccineMatch = true;
                                    break;
                                }
                            }
                        }

                        // Advanced keyword matching
                        if (!vaccineMatch)
                        {
                            var vaccineKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                            foreach (var vaccine in immunizationNames)
                            {
                                var words = vaccine.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                        vaccineKeywords.Add(word);
                                }
                            }

                            var alertKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                            // Add keywords from name, description, and vaccine set
                            foreach (var field in new[] { alert.Name, alert.Description, alert.OrderSet })
                            {
                                if (!string.IsNullOrEmpty(field))
                                {
                                    var words = field.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                    foreach (var word in words)
                                    {
                                        if (word.Length > 2)
                                            alertKeywords.Add(word);
                                    }
                                }
                            }

                            // Check for keyword matches
                            foreach (var vaccineKeyword in vaccineKeywords)
                            {
                                if (alertKeywords.Contains(vaccineKeyword))
                                {
                                    vaccineMatch = true;
                                    break;
                                }
                            }
                        }

                        // Final matching with age and gender checks
                        if (vaccineMatch)
                        {
                            bool ageMatch = true;
                            if (alert.AgeLowerBound.HasValue && patientAge < alert.AgeLowerBound.Value)
                                ageMatch = false;
                            if (alert.AgeUpperBound.HasValue && patientAge > alert.AgeUpperBound.Value)
                                ageMatch = false;

                            bool genderMatch = true;
                            if (!string.IsNullOrEmpty(alert.Gender) && alert.Gender != "Both")
                            {
                                if (_PatientData.Sex != alert.Gender)
                                    genderMatch = false;
                            }

                            if (ageMatch && genderMatch)
                            {
                                matchingAlerts.Add(alert);
                            }
                        }
                    }
                }

                return matchingAlerts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving custom immunization alerts");
                return new List<CustomImmunizationAlerts>();
            }
        }

        // Helper method to get immunization severity
        private async Task<string> GetImmunizationSeverity(
            string immunizationName,
            int patientAge,
            string ageRecommendation)
        {
            string prompt = $"What is the severity level (Low, Medium, High) of giving the {immunizationName} vaccine " +
                           $"to a {patientAge}-year-old patient when the recommended age is {ageRecommendation}? " +
                           $"Explain the potential risks or considerations.";

            return await AskGptModel(prompt);
        }

        // Existing method to extract severity level
        private static string ExtractSeverityLevel(string response)
        {
            response = response.ToLower();

            if (response.Contains("critical") || response.Contains("severe") || response.Contains("high"))
                return "High";
            else if (response.Contains("medium") || response.Contains("moderate"))
                return "Medium";
            else if (response.Contains("low") || response.Contains("minor"))
                return "Low";
            else
                return "Medium";
        }
        private async Task<string> GetDetailedAgeWarning(string vaccineName, int patientAge, string ageRecommendation)
        {
            try
            {
                string patientName = _PatientData.Name;
                string severityLevel = "MEDIUM";
                string reason = "Not available";

                try
                {
                    var matches = System.Text.RegularExpressions.Regex.Matches(
                        ageRecommendation,
                        @"(\d+)\s*(weeks?|months?|years?)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    List<double> agesInYears = new List<double>();

                    foreach (System.Text.RegularExpressions.Match match in matches)
                    {
                        int number = int.Parse(match.Groups[1].Value);
                        string unit = match.Groups[2].Value.ToLower();

                        double ageInYears = unit switch
                        {
                            "week" or "weeks" => number / 52.0,
                            "month" or "months" => number / 12.0,
                            "year" or "years" => number,
                            _ => 0
                        };

                        agesInYears.Add(ageInYears);
                    }

                    if (agesInYears.Count > 0)
                    {
                        double closestAge = agesInYears.OrderBy(a => Math.Abs(a - patientAge)).First();
                        double ageDiff = Math.Abs(patientAge - closestAge);

                        if (ageDiff <= 1) severityLevel = "LOW";
                        else if (ageDiff <= 5) severityLevel = "MEDIUM";
                        else severityLevel = "HIGH";
                    }

                    // 🧠 Get reason from GPT
                    reason = await GetReasonFromGpt(vaccineName, patientAge, ageRecommendation);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Age recommendation processing error");
                }

                return $"Name/Age: {patientName}/{patientAge}\n" +
                       $"Reason: {reason}\n" +
                       $"Age Recommendation: {ageRecommendation}\n" +
                       $"Severity Level: {severityLevel}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting detailed age warning");
                string patientName = _PatientData.Name;

                return $"Name/Age: {patientName}\n" +
                       $"Age: {patientAge}\n" +
                       $"Reason: Unable to determine reason\n" +
                       $"Age Recommendation: {ageRecommendation}\n" +
                       $"Severity Level: MEDIUM";
            }
        }

        /// <summary>
        /// Uses GPT to explain briefly why a vaccine is not recommended for the given age.
        /// </summary>
        private async Task<string> GetReasonFromGpt(string vaccineName, int patientAge, string ageRecommendation)
        {
            string prompt = $"The {vaccineName} vaccine is not recommended for the patient. " +
                            $"Briefly explain in 1 simple sentence not more than 17 words.";

            try
            {
                return await AskGptModel(prompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating GPT-based reason");
                return "The vaccine may cause reduced effectiveness or increased risk of adverse reactions due to age mismatch.";
            }
        }

        /// <summary>
        /// Gets age recommendation for a specific vaccine using GPT model
        /// </summary>
        private async Task<string> GetVaccineAgeRecommendation(string vaccineName)
        {
            try
            {
                string prompt = $"What is the recommended age range for the {vaccineName} vaccine? Answer briefly with just the age range.Just only answer the recommended age with no additional information";
                return await AskGptModel(prompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vaccine age recommendation");
                return "Unknown";
            }
        }

        /// <summary>
        /// Checks if the vaccine is appropriate for the patient's age
        /// </summary>
        private bool IsVaccineAgeAppropriate(string ageRecommendation, int patientAge)
        {
            try
            {
                if (string.IsNullOrEmpty(ageRecommendation) || ageRecommendation == "Unknown")
                    return true;
                string lowerRec = ageRecommendation.ToLower();

                if (lowerRec.Contains("birth") || lowerRec.Contains("newborn"))
                {
                    return patientAge < 1;
                }

                if (lowerRec.Contains("month"))
                {
                    var monthMatches = System.Text.RegularExpressions.Regex.Match(lowerRec, @"(\d+)(?:\s*-\s*(\d+))?\s*month");
                    if (monthMatches.Success)
                    {
                        int minMonths = int.Parse(monthMatches.Groups[1].Value);

                        int patientMonths = patientAge * 12;

                        if (monthMatches.Groups[2].Success)
                        {
                            int maxMonths = int.Parse(monthMatches.Groups[2].Value);
                            return patientMonths >= minMonths && patientMonths <= maxMonths;
                        }

                        return patientMonths == minMonths;
                    }
                }

                if (lowerRec.Contains("infant"))
                    return patientAge < 2;

                if (lowerRec.Contains("toddler"))
                    return patientAge >= 1 && patientAge <= 3;

                if (lowerRec.Contains("preschool"))
                    return patientAge >= 3 && patientAge <= 5;

                if (lowerRec.Contains("children") || lowerRec.Contains("child"))
                    return patientAge < 18;

                if (lowerRec.Contains("adolescent") || lowerRec.Contains("teen"))
                    return patientAge >= 10 && patientAge <= 19;

                if (lowerRec.Contains("adult"))
                    return patientAge >= 18;

                if (lowerRec.Contains("elderly") || lowerRec.Contains("senior"))
                    return patientAge >= 65;

                if (lowerRec.Contains("-"))
                {
                    var parts = lowerRec.Split('-');
                    if (parts.Length >= 2)
                    {
                        var minAgeMatch = System.Text.RegularExpressions.Regex.Match(parts[0].Trim(), @"(\d+)");
                        if (minAgeMatch.Success && int.TryParse(minAgeMatch.Groups[1].Value, out int minAge))
                        {
                            var maxAgeMatch = System.Text.RegularExpressions.Regex.Match(parts[1].Trim(), @"(\d+)");
                            if (maxAgeMatch.Success && int.TryParse(maxAgeMatch.Groups[1].Value, out int maxAge))
                            {
                                return patientAge >= minAge && patientAge <= maxAge;
                            }
                            return patientAge >= minAge;
                        }
                    }
                }

                var specificAgeMatch = System.Text.RegularExpressions.Regex.Match(lowerRec, @"at\s+age\s+(\d+)");
                if (specificAgeMatch.Success)
                {
                    int specificAge = int.Parse(specificAgeMatch.Groups[1].Value);
                    return patientAge == specificAge;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking vaccine age appropriateness: {Message}", ex.Message);
                return true;
            }
        }

        /// <summary>
        /// Asks the GPT model a question and returns the response
        /// </summary>
        /// <param name="prompt">Question to ask the GPT model</param>
        /// <returns>GPT model response</returns>
        private async Task<string> AskGptModel(string prompt)
        {
            try
            {
                string systemMessage = "You are a medical assistant with expertise in pediatric and adult immunizations. " +
                                        "Provide concise, accurate clinical information about vaccines, their recommended age ranges, " +
                                        "and potential risks of administering vaccines outside their recommended age ranges.";

                return await MeasureService.AskGptAsync(systemMessage, prompt);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling GPT model: {Message}", ex.Message);
                return "Error determining age appropriateness.";
            }
        }

        private async Task CancelData()
        {
            deletesurgerylist.Clear();
            AddList.Clear();

            var existingImmunizations = await _ImmunizationService.GetImmunizationByIdAsyncAndIsActive(PatientId, OrgID, Subscription);

            immunization = existingImmunizations?.Where(i => !string.IsNullOrEmpty(i.Immunizations)).ToList() ?? new List<ImmunizationData>();

            int emptyRowsNeeded = 9 - immunization.Count;
            if (emptyRowsNeeded > 0)
            {
                immunization.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                    .Select(_ => new ImmunizationData
                    {
                        Immunizations = string.Empty,
                        CPTCode = string.Empty,
                        CVXCode = string.Empty,
                        Comments = string.Empty
                    }));
            }
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        private async Task OnVaccineChanged(Vaccines vaccine)
        {
            SelectedVaccine = vaccine;
            if (vaccine != null)
            {
                VaccineName = vaccine.VaccineName; // Add this line
                SelectedCPTCode = vaccine.CPTCode;
                SelectedCVXCode = vaccine.CVXCode;
                SelectedCPTDescription = vaccine.CPTDescription;
            }
            else
            {
                VaccineName = string.Empty; // Add this line
                SelectedCPTCode = string.Empty;
                SelectedCVXCode = string.Empty;
                SelectedCPTDescription = string.Empty;
            }
            StateHasChanged();
        }

        private async Task OnFDBVaccineSelected(FDBVaccines vaccine)
        {
            FDBSelectedVaccine = vaccine;

            if (vaccine == null)
            {
                VaccineName = string.Empty;
                SelectedCPTCode = string.Empty;
                SelectedCVXCode = string.Empty;
                SelectedCPTDescription = string.Empty;
                StateHasChanged();
                return;
            }

            VaccineName = vaccine.EVD_CVX_CD_DESC_SHORT;

            try
            {
                var CPTForVaccine = await FDBService.GetCPTForVaccine(vaccine.EVD_CVX_CD);

                if (CPTForVaccine != null)
                {
                    SelectedCPTCode = CPTForVaccine.EVD_CPT_CD;
                    SelectedCVXCode = vaccine.EVD_CVX_CD;
                    SelectedCPTDescription = vaccine.EVD_CVX_CD_DESC_LONG;
                }
                else
                {
                    SelectedCPTCode = string.Empty;
                    SelectedCVXCode = vaccine.EVD_CVX_CD;
                    SelectedCPTDescription = vaccine.EVD_CVX_CD_DESC_LONG;
                    _logger?.LogWarning($"No CPT found for CVX code: {vaccine.EVD_CVX_CD}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"Error getting CPT for vaccine CVX: {vaccine.EVD_CVX_CD}");
                SelectedCPTCode = string.Empty;
                SelectedCVXCode = vaccine.EVD_CVX_CD;
                SelectedCPTDescription = vaccine.EVD_CVX_CD_DESC_LONG;
            }

            StateHasChanged();
        }

        private void ResetInputFields()
        {
            FDBSelectedVaccine = null;
            SelectedVaccine = null;

            VaccineName = string.Empty;
            SelectedCPTCode = string.Empty;
            SelectedCVXCode = string.Empty;
            SelectedCPTDescription = string.Empty;
            Comments = string.Empty;
            GivenDate = DateTime.Now;

            // Clear any cached search results
            _details?.Clear();
            FDBVaccineList?.Clear();

            StateHasChanged();
        }
        private async Task<bool> CheckConfiguredAlertsOnly(List<ImmunizationData> immunizationsToCheck)
        {
            try
            {
                var configuredAlerts = await GetCustomImmunizationAlerts(immunizationsToCheck);

                if (configuredAlerts != null && configuredAlerts.Any())
                {
                    StringBuilder alertMessage = new StringBuilder();
                    alertMessage.AppendLine("Age Inappropriate Warning.Do you want to add this immunization?"); // CHANGED MESSAGE

                    bool? confirmResult = await DialogService.ShowMessageBox(
                        _localizer["Alert Warning"],
                        alertMessage.ToString(),
                        yesText: _localizer["Add Anyway"], // CHANGED BUTTON TEXT
                        noText: _localizer["Cancel"]);     // CHANGED BUTTON TEXT
                    return confirmResult == true;
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking configured alerts");
                return true;
            }
        }
        private async Task AddNewSurgery()
        {
            try
            {
                if (patientAge == 0)
                {
                    Snackbar.Add(_localizer["Patient is too young to be prescribed an immunization"], Severity.Warning);
                    return;
                }
                if (string.IsNullOrEmpty(VaccineName))
                {
                    Snackbar.Add(_localizer["Please Select Vaccine"], Severity.Warning);
                    return;
                }
                var tempImmunization = new ImmunizationData
                {
                    ImmunizationId = Guid.NewGuid(),
                    PatientId = PatientId,
                    PCPId = Guid.Parse(User.id),
                    OrganizationId = PatientId,
                    CreatedBy = Guid.Parse(User.id),
                    UpdatedBy = PatientId,
                    CreatedDate = GivenDate,
                    UpdatedDate = DateTime.Now,
                    GivenDate = GivenDate,
                    Immunizations = VaccineName,
                    CPTCode = SelectedCPTCode,
                    CVXCode = SelectedCVXCode,
                    CPTDescription = SelectedCPTDescription,
                    Comments = Comments,
                    IsActive = true
                };
                var tempList = new List<ImmunizationData> { tempImmunization };
                var ageRecommendation = await GetVaccineAgeRecommendation(VaccineName);
                var isAgeAppropriate = IsVaccineAgeAppropriate(ageRecommendation, patientAge);

                bool hasAgeIssue = !isAgeAppropriate;
                if (!hasAgeIssue)
                {
                    bool proceedWithConfiguredAlerts = await CheckConfiguredAlertsOnly(tempList);
                    if (!proceedWithConfiguredAlerts)
                    {
                        return;
                    }
                }
                else
                {
                    bool? confirmResult = await DialogService.ShowMessageBox(
                        _localizer["Age Inappropriate Alert"],
                        _localizer["The vaccine is not recommended for this age. Do you still want to continue?"],
                        yesText: _localizer["Add Anyway"],
                        noText: _localizer["Cancel"]);

                    if (confirmResult != true)
                    {
                        return; // User chose not to proceed with age issue
                    }
                }
                var emptyRow = immunization.FirstOrDefault(i => string.IsNullOrEmpty(i.Immunizations));

                if (emptyRow == null)
                {
                    emptyRow = new ImmunizationData();
                    immunization.Add(emptyRow);
                }

                emptyRow.ImmunizationId = tempImmunization.ImmunizationId;
                emptyRow.PatientId = tempImmunization.PatientId;
                emptyRow.PCPId = tempImmunization.PCPId;
                emptyRow.OrganizationId = tempImmunization.OrganizationId;
                emptyRow.CreatedBy = tempImmunization.CreatedBy;
                emptyRow.UpdatedBy = tempImmunization.UpdatedBy;
                emptyRow.CreatedDate = tempImmunization.CreatedDate;
                emptyRow.UpdatedDate = tempImmunization.UpdatedDate;
                emptyRow.GivenDate = tempImmunization.GivenDate;
                emptyRow.Immunizations = tempImmunization.Immunizations;
                emptyRow.CPTCode = tempImmunization.CPTCode;
                emptyRow.CVXCode = tempImmunization.CVXCode;
                emptyRow.CPTDescription = tempImmunization.CPTDescription;
                emptyRow.Comments = tempImmunization.Comments;
                emptyRow.IsActive = tempImmunization.IsActive;

                AddList.Add(emptyRow);

                if (ImmunizationGrid != null)
                {
                    await ImmunizationGrid.Refresh();
                }
                var generatedAlerts = await CheckImmunizationsForAlerts(new List<ImmunizationData> { emptyRow });
                if (generatedAlerts != null && generatedAlerts.Any())
                {
                    pendingAlerts.AddRange(generatedAlerts);
                }

                ResetInputFields();
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new immunization");
                Snackbar.Add(_localizer["Failed to add immunization"], Severity.Error);
            }
        }

        private string GenerateRichTextContent(string manualData)
        {

            string immunizationContent = immunization != null
                ? string.Join(" ", immunization
                    .Where(s => !string.IsNullOrEmpty(s.Immunizations))
                    .OrderByDescending(s => s.CreatedDate)
                    .Select(s => $"<ul><li style='margin-left: 20px;'><b>{(s.CreatedDate.HasValue ? s.CreatedDate.Value.ToString("yyyy-MM-dd") : "N/A")}</b> : " +
                                $"Immunization: {s.Immunizations}, CPT: {s.CPTCode}, CVX: {s.CVXCode}" +
                                (string.IsNullOrEmpty(s.Comments) ? "" : $", Comments: {s.Comments}") + "</li></ul>"))
                : string.Empty;

            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {immunizationContent}
            </div>
            </div>";
        }






        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}