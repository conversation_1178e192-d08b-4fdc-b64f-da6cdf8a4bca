﻿@page "/VisionExamination"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject TeyaUIViewModels.ViewModel.IVisionExaminationService VisionExaminationService
@using Syncfusion.Blazor.RichTextEditor
@inject IDialogService DialogService
@using Syncfusion.Blazor.Inputs
@layout Admin

@* <SfRichTextEditor Value="@editorContent" @ref="RichTextEditor" ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))"> *@
@*     <RichTextEditorToolbarSettings Items="@Tools"> *@
@*         <RichTextEditorCustomToolbarItems> *@
@*             <RichTextEditorCustomToolbarItem Name="add"> *@
@*                 <Template> *@
@*                     <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" aria-label="edit" OnClick="OpenDialog" /> *@
@*                 </Template> *@
@*             </RichTextEditorCustomToolbarItem> *@
@*         </RichTextEditorCustomToolbarItems> *@
@*     </RichTextEditorToolbarSettings> *@
@* </SfRichTextEditor> *@


<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="add">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                               Size="Size.Small"
                                               OnClick="OpenDialog" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>


<MudDialog Visible="isDialogOpen" Style="width: 85vw; max-width: 1000px;">
    <TitleContent>
        <MudText Typo="Typo.h6">Vision Examination</MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CloseDialog"
                       Style="position: absolute; right: 16px; top: 6px;" />
    </TitleContent>

    <DialogContent>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="OpenVisionScreeningTab" Style="margin-bottom: 3px;height: 28px;">
            New
        </MudButton>
        <MudTabs @bind-ActivePanelIndex="@activeTabIndex">
            <MudTabPanel Text="All Records" Class="custom-tab-panel">
                    <SfGrid @ref="visionRxGrid" TValue="VisionRx" DataSource="@VisionDataList"
                        AllowPaging="true" GridLines="GridLine.Both">

                        <GridEditSettings AllowEditing="true" AllowDeleting="true" AllowAdding="true" Mode="EditMode.Normal"></GridEditSettings>
                        <GridPageSettings PageSize="5"></GridPageSettings>

                        <GridEvents TValue="VisionRx" RowSelected="OnRowSelected" OnActionBegin="OnActionBegin" RowDataBound="OnRowDataBound"></GridEvents>

                        <GridColumns>
                            <GridColumn Field="ExaminationId" IsPrimaryKey="true" HeaderText="@Localizer["Examination Id"]" Width="350" TextAlign="TextAlign.Center"></GridColumn>
                            <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" Format="MM/dd/yyyy" Width="150" TextAlign="TextAlign.Center"></GridColumn>
                            <GridColumn Field="UpdatedDate" HeaderText="@Localizer["Updated Date"]" Format="MM/dd/yyyy" Width="150" TextAlign="TextAlign.Center"></GridColumn>
                            <GridColumn HeaderText="Actions" Width="150" TextAlign="TextAlign.Center">
                                <GridCommandColumns>
                                    <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions()
                                         { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                                </GridCommandColumns>
                            </GridColumn>
                        </GridColumns>
                    </SfGrid>
            </MudTabPanel>
            <MudTabPanel Text="Vision Screening" Class="custom-tab-panel">
                <MudGrid Spacing="3" Class="mb-1">
                    <MudItem xs="4">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Unaided Acuities</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="2"></MudItem> 
                                <MudItem xs="3"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">DVA</MudText></MudItem>
                                <MudItem xs="3"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">NVA</MudText></MudItem>
                                <MudItem xs="3"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">PH</MudText></MudItem>

                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OD</MudText></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedDvaOD" Dense /></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedNvaOD" Dense /></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedPhOD" Dense /></MudItem>

                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OS</MudText></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedDvaOS" Dense /></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedNvaOS" Dense /></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedPhOS" Dense /></MudItem>

                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OU</MudText></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedDvaOU" Dense /></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedNvaOU" Dense /></MudItem>
                                <MudItem xs="3"><SfTextBox @bind-Value="VisionData.UnaidedPhOU" Dense /></MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>

                    <MudItem xs="4">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">K-Readings</MudText>
                                </MudItem>

                                <MudItem xs="12" Class="d-flex align-center">
                                    <MudRadioGroup T="string" @bind-SelectedOption="VisionData.UnaidedSIUnit" ValueChanged="HandleSelectionChange">
                                        <MudRadio T="string" Value="Diopter">Diopter</MudRadio>
                                        <MudRadio T="string" Value="MM">MM</MudRadio>
                                    </MudRadioGroup>
                                </MudItem>

                                <MudItem xs="5" Class="d-flex justify-center">
                                    <MudText Typo="Typo.body1" Class="text-center">Flat K</MudText>
                                </MudItem>
                                <MudItem xs="5" Class="d-flex justify-center">
                                    <MudText Typo="Typo.body1" Class="text-center">Steep K</MudText>
                                </MudItem>

                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.UnaidedFlatKOD" Placeholder="OD" CssClass="text-center" /></MudItem>

                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.UnaidedFlatKOS" Placeholder="OD" CssClass="text-center" /></MudItem>

                                <MudItem xs="5"><SfNumericTextBox @bind-Value="VisionData.UnaidedSteepKOD" Placeholder="OS" CssClass="text-center" /></MudItem>

                                <MudItem xs="5"><SfNumericTextBox @bind-Value="VisionData.UnaidedSteepKOS" Placeholder="OS" CssClass="text-center" /></MudItem>
                            </MudGrid>

                        </MudPaper>
                    </MudItem>
                    <MudItem xs="4">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Pupillary Distance</MudText>
                                </MudItem>

                                <MudItem xs="2"></MudItem> 
                                <MudItem xs="5"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">Dist</MudText></MudItem>
                                <MudItem xs="5"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">Near</MudText></MudItem>

                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OU</MudText></MudItem>
                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.PdDistOU" Dense /></MudItem>
                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.PdNearOU" Dense /></MudItem>

                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OD</MudText></MudItem>
                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.PdDistOD" Dense /></MudItem>
                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.PdNearOD" Dense /></MudItem>

                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OS</MudText></MudItem>
                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.PdDistOS" Dense /></MudItem>
                                <MudItem xs="5"><SfNumericTextBox  @bind-Value="VisionData.PdNearOS" Dense /></MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="2">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Vertex Dist</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="10"><SfNumericTextBox @bind-Value="VisionData.UnaidedVertexSpec" Dense /></MudItem>
                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">MM</MudText></MudItem>

                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="2">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Spec BC</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="10"><SfNumericTextBox @bind-Value="VisionData.UnaidedSpecBC" Dense /></MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="2">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Work Dist</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="10"><SfNumericTextBox @bind-Value="VisionData.UnaidedWorkDist" Dense /></MudItem>
                                <MudItem xs="2" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">IN</MudText></MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                </MudGrid>

            </MudTabPanel>
            <MudTabPanel Text="Prescription & Correction" Class="custom-tab-panel">
                <MudGrid Spacing="3" Class="mb-1">
                    <MudItem xs="8">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Presenting Spectacle RX</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="1"></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">SPH</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">CYL</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">AXIS</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">H PRISM</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">V PRISM</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">ADD</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">DVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">NVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">PH</MudText></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OD</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleSphOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleCylOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleAxisOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.PresentingSpectacleHPrismOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.PresentingSpectacleVPrismOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleAddOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleDvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleNvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectaclePhOD" Dense /></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OS</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleSphOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleCylOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleAxisOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.PresentingSpectacleHPrismOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.PresentingSpectacleVPrismOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleAddOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleDvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleNvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectaclePHOS" Dense /></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OU</MudText></MudItem>
                                <MudItem xs="8"></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleDvaOU" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectacleNvaOU" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.PresentingSpectaclePhOU" Dense /></MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="4">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Auto Refraction</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>


                                <MudGrid>
                                    <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">SPH</MudText></MudItem>
                                    <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">CYL</MudText></MudItem>
                                    <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">AXIS</MudText></MudItem>
                                    <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">ADD</MudText></MudItem>
                                    <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">DVA</MudText></MudItem>
                                    <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">NVA</MudText></MudItem>
                                </MudGrid>

                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionSphOD" Placeholder="OD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionCylOD" Placeholder="OD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionAxisOD" Placeholder="OD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionAddOD" Placeholder="OD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionDvaOD" Placeholder="OD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionNvaOD" Placeholder="OD" Dense /></MudItem>

                         
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionSphOS" Placeholder="OS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionCylOS" Placeholder="OS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionAxisOS" Placeholder="OS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionAddOS" Placeholder="OS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionDvaOS" Placeholder="OS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.RefractionNvaOS" Placeholder="OS" Dense /></MudItem>


                            </MudGrid>
                        </MudPaper>
                    </MudItem>

                    <MudItem xs="8">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Manifest</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="1"></MudItem> 
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">SPH</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">CYL</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">AXIS</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">H PRISM</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">V PRISM</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">ADD</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">DVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">NVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">PH</MudText></MudItem>
                               
                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OD</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestSphOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestCylOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestAxisOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.ManifestHPrismOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.ManifestVPrismOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestAddOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestDvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestNvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestPhOD" Dense /></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OS</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestSphOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestCylOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestAxisOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.ManifestHPrismOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.ManifestVPrismOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestAddOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestDvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestNvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.ManifestPHOS" Dense /></MudItem>


                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="8">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Cycloplegic</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="1"></MudItem> 
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">SPH</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">CYL</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">AXIS</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">H PRISM</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">V PRISM</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">ADD</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">DVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">NVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">PH</MudText></MudItem>
                                
                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OD</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicSphOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicCylOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicAxisOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.CycloplegicHPrismOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.CycloplegicVPrismOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicAddOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicDvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicNvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicPhOD" Dense /></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OS</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicSphOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicCylOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicAxisOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.CycloplegicHPrismOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.CycloplegicVPrismOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicAddOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicDvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicNvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.CycloplegicPHOS" Dense /></MudItem>

                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="8">
                        <MudPaper Class="pa-2">
                            <MudGrid Spacing="1">
                                <MudItem xs="12">
                                    <MudText Typo="Typo.h6">Final Spectacle RX</MudText>
                                </MudItem>

                                <MudItem xs="12"></MudItem>

                                <MudItem xs="1"></MudItem> 
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">SPH</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">CYL</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">AXIS</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">H PRISM</MudText></MudItem>
                                <MudItem xs="2"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">V PRISM</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">ADD</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">DVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">NVA</MudText></MudItem>
                                <MudItem xs="1"><MudText Typo="Typo.body1" Class="d-flex align-center justify-center">PH</MudText></MudItem>
                                
                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OD</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleSphOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleCylOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleAxisOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.FinalSpectacleHPrismOD" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.FinalSpectacleVPrismOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleAddOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleDvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleNvaOD" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectaclePhOD" Dense /></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OS</MudText></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleSphOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleCylOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleAxisOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.FinalSpectacleHPrismOS" Dense /></MudItem>
                                <MudItem xs="2"><SfTextBox @bind-Value="VisionData.FinalSpectacleVPrismOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleAddOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleDvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleNvaOS" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectaclePHOS" Dense /></MudItem>

                                <MudItem xs="1" Class="d-flex align-center justify-center"><MudText Typo="Typo.body1">OU</MudText></MudItem>
                                <MudItem xs="8"></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleDvaOU" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectacleNvaOU" Dense /></MudItem>
                                <MudItem xs="1"><SfTextBox @bind-Value="VisionData.FinalSpectaclePhOU" Dense /></MudItem>
                            </MudGrid>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </MudTabPanel>
              
        </MudTabs>
    </DialogContent>

    <DialogActions>
        <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 6px 12px; border-top: 1px solid #E0E0E0;">
            <MudButton Color="Color.Secondary"
                       Variant="Variant.Outlined"
                       OnClick="CancelChanges"
                       Dense="true"
                       Style="min-width: 120px; height: 37px; font-weight: 500;">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Color="Color.Primary"
                       Variant="Variant.Filled"
                       OnClick="SaveChanges"
                       Dense="true"
                       Style="min-width: 120px; height: 37px; font-weight: 500;">
                @Localizer["Save"]
            </MudButton>
        </div>
    </DialogActions>
</MudDialog>

<style>
    .selected-row {
        background-color: #cce5ff !important;
    }

    .custom-tab-panel {
        height: 25px; /* or whatever height you need */
        padding: 2px;
    }

    .custom-title-content {
        height: 20px;
        padding: 2px;
    }
}

</style>

<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>