﻿@page "/Security"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "SecurityAccessPolicy")]
@using TeyaUIViewModels.ViewModel
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@layout Admin
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.ViewModel

<div class="security-settings-container">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <MudIcon Icon="@Icons.Material.Filled.Security" Class="header-icon" />
            <div class="header-text">
                <MudText Typo="Typo.h6" Class="header-title">@Localizer["Security Settings"]</MudText>
            </div>
        </div>
    </div>

    <!-- Organization Selection Section -->
    <MudPaper Class="section-card organization-section" Elevation="2">
        <div class="section-header">
            <MudIcon Icon="@Icons.Material.Filled.Business" Class="section-icon" />
            <MudText Typo="Typo.h6" Class="section-title">@Localizer["SelectOrganization"]</MudText>
        </div>

        @if (Organizations != null && Organizations.Any())
        {
            <div class="organization-content">
                <div class="organization-selection-row">
                    <div class="autocomplete-container">
                        <MudAutocomplete T="Organization"
                                         @bind-Value="SelectedOrganization"
                                         SearchFunc="@SearchOrganizations"
                                         ToStringFunc="@(org => org?.OrganizationName ?? string.Empty)"
                                         Label="@Localizer["SearchSelectOrganization"]"
                                         Variant="Variant.Outlined"
                                         Dense="true"
                                         Clearable="true"
                                         ResetValueOnEmptyText="true"
                                         CoerceText="false"
                                         CoerceValue="false"
                                         MaxItems="null"
                                         ShowProgressIndicator="true"
                                         Class="organization-autocomplete">
                            <ItemTemplate Context="org">
                                <div class="organization-item">
                                    <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Small" Class="organization-item-icon" />
                                    <MudText Typo="Typo.body2">@org.OrganizationName</MudText>
                                </div>
                            </ItemTemplate>
                        </MudAutocomplete>
                    </div>
                    @if (SelectedOrganization != null)
                    {
                        <div class="selected-organization">
                            <MudChip T="string"
                                     Color="Color.Success"
                                     Size="Size.Medium"
                                     Icon="@Icons.Material.Filled.CheckCircle"
                                     Class="selected-chip">
                                @Localizer["Selected"]: @SelectedOrganization.OrganizationName
                            </MudChip>
                        </div>
                    }
                </div>
            </div>
        }
        else
        {
            <div class="empty-state">
                <MudIcon Icon="@Icons.Material.Filled.BusinessCenter" Class="empty-icon" />
                <MudText Typo="Typo.body1" Class="empty-text">@Localizer["NoOrganizationsAvailable"]</MudText>
            </div>
        }
    </MudPaper>

    <!-- Product Management Section -->
    @if (SelectedOrganization != null && Products != null)
    {
        <MudPaper Class="section-card product-management-section" Elevation="2">
            <div class="section-header">
                <MudIcon Icon="@Icons.Material.Filled.Inventory" Class="section-icon" />
                <MudText Typo="Typo.h6" Class="section-title">
                    @Localizer["ManageProductsFor"]: <span class="organization-name">@SelectedOrganization.OrganizationName</span>
                </MudText>
            </div>

            <!-- Add Product Section -->
            <div class="add-product-section">
                <div class="product-selection-row">
                    <div class="product-select-container">
                        <MudSelect T="Guid?"
                                   @bind-Value="SelectedProductToAdd"
                                   Label="@Localizer["SelectProductToAdd"]"
                                   Variant="Variant.Outlined"
                                   Dense="true"
                                   Class="product-select">
                            @if (AvailableProducts?.Any() == true)
                            {
                                @foreach (var product in AvailableProducts)
                                {
                                    <MudSelectItem T="Guid?" Value="@(product.Id)">
                                        <div class="product-item">
                                            <MudIcon Icon="@Icons.Material.Filled.Apps" Size="Size.Small" Class="product-item-icon" />
                                            <MudText Typo="Typo.body2">@product.Name</MudText>
                                        </div>
                                    </MudSelectItem>
                                }
                            }
                            else
                            {
                                <MudSelectItem T="Guid?" Value="null" Disabled="true">
                                    @(AvailableProducts == null
                                        ? Localizer["LoadingProducts"]
                                        : Localizer["NoProductsAvailable"])
                                </MudSelectItem>
                            }
                        </MudSelect>
                    </div>
                    @if (SelectedProductToAdd != null)
                    {
                        <MudButton Variant="Variant.Filled"
                                   StartIcon="@Icons.Material.Filled.Add"
                                   Color="Color.Primary"
                                   Size="Size.Medium"
                                   OnClick="AddProductToOrganization"
                                   Disabled="IsAdding"
                                   Class="add-product-button">
                            @if (IsAdding)
                            {
                                <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                <span class="ml-2">@Localizer["Adding"]</span>
                            }
                            else
                            {
                                <span>@Localizer["AddProduct"]</span>
                            }
                        </MudButton>
                    }
                </div>
            </div>

            <!-- Products Grid Section -->
            <div class="products-grid-section">
                <SfGrid @ref="ProductGrid"
                        DataSource="@Products"
                        AllowPaging="true"
                        AllowSorting="true"
                        CssClass="enhanced-grid">
                    <GridPageSettings PageSize="10" PageSizes="true" />
                    <GridColumns>
                        <GridColumn Field="@nameof(Product.Id)" HeaderText="ID" Visible="false" />
                        <GridColumn Field="@nameof(Product.Name)" HeaderText="@Localizer["ProductName"]" TextAlign="TextAlign.Left" Width="200">
                            <Template>
                                <div class="product-name-cell">
                                    <MudIcon Icon="@Icons.Material.Filled.Apps" Size="Size.Small" Class="product-cell-icon" />
                                    <span>@((context as Product)?.Name)</span>
                                </div>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="@nameof(Product.Description)" HeaderText="@Localizer["Description"]" TextAlign="TextAlign.Left" />
                        <GridColumn Field="@nameof(Product.Byproduct)" HeaderText="@Localizer["Byproduct"]" TextAlign="TextAlign.Center" Width="140">
                            <Template>
                                <MudChip T="string"
                                         Size="Size.Small"
                                         Color="@(string.IsNullOrEmpty((context as Product)?.Byproduct) ? Color.Default : Color.Info)"
                                         Class="byproduct-chip">
                                    @((context as Product)?.Byproduct ?? "N/A")
                                </MudChip>
                            </Template>
                        </GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" Width="100" TextAlign="TextAlign.Center">
                            <Template>
                                <MudTooltip Text="@Localizer["RemoveProductAccess"]">
                                    <MudButton Variant="Variant.Text"
                                               StartIcon="@Icons.Material.Filled.Delete"
                                               Color="Color.Error"
                                               Size="Size.Small"
                                               OnClick="@(() => RemoveProductFromOrganization((Product)context))"
                                               Disabled="@IsRemoving"
                                               Class="remove-button">
                                        @if (IsRemoving)
                                        {
                                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                        }
                                    </MudButton>
                                </MudTooltip>
                            </Template>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
        </MudPaper>
    }

    
    @if (SelectedOrganization != null && (Products == null || !Products.Any()))
    {
        <MudPaper Class="section-card empty-products-section" Elevation="1">
            <div class="empty-state">
                <MudIcon Icon="@Icons.Material.Filled.ProductionQuantityLimits" Class="empty-icon" />
                <MudText Typo="Typo.h6" Class="empty-title">@Localizer["NoProductsFound"]</MudText>
                <MudText Typo="Typo.body2" Class="empty-subtitle">@Localizer["AddProductsToGetStarted"]</MudText>
            </div>
        </MudPaper>
    }
</div>

<style>
    .security-settings-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 24px;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        min-height: calc(100vh - 120px);
    }

    .page-header {
        margin-bottom: 32px;
    }

    .header-content {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 24px 0;
    }

    .header-icon {
        font-size: 1.8rem !important;
        color: var(--mud-palette-primary);
    }

    .header-title {
        font-weight: 600 !important;
        color: var(--mud-palette-text-primary);
        margin: 0 !important;
        font-size: 1.25rem !important; 
    }

    .section-title {
        font-weight: 600 !important;
        color: var(--mud-palette-text-primary);
        margin: 0 !important;
        font-size: 1.25rem !important; 
    }

    .section-card {
        margin-bottom: 24px;
        border-radius: 16px !important;
        padding: 24px;
        background: white;
        border: 1px solid #e2e8f0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    }

        .section-card:hover {
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
        }

    .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 2px solid #f1f5f9;
    }

    .section-icon {
        font-size: 1.5rem !important;
        color: var(--mud-palette-primary);
    }

    .section-title {
        font-weight: 600 !important;
        color: var(--mud-palette-text-primary);
        margin: 0 !important;
    }

    .organization-name {
        color: var(--mud-palette-primary);
        font-weight: 600;
    }

    .organization-content {
        padding: 8px 0;
    }

    .organization-selection-row {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .autocomplete-container {
        width: 320px;
    }

    .organization-autocomplete,
    .product-select {
        background: white;
        border-radius: 8px;
        width: 100%;
    }

        .organization-autocomplete .mud-input-root {
            height: 12px !important;
        }

        .product-select .mud-input-root {
            height: 12px !important;
        }

    .product-selection-row {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .product-select-container {
        width: 320px;
    }

    .organization-item,
    .product-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 4px;
        font-size: 0.875rem;
    }

    .organization-item-icon,
    .product-item-icon {
        color: var(--mud-palette-primary);
        font-size: 1rem !important;
    }

    /* Autocomplete and Select Dropdown Styling */
    .mud-popover-paper {
        max-height: 240px !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    }

    .mud-list-item {
        padding: 8px 12px !important;
        font-size: 0.875rem !important;
        min-height: 36px !important;
    }

        .mud-list-item:hover {
            background-color: #f8fafc !important;
        }

    .selected-organization {
        display: flex;
        justify-content: flex-start;
    }

    .selected-chip {
        border-radius: 20px !important;
        font-weight: 500 !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .add-product-section {
        background: #f8fafc;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        border: 1px solid #e2e8f0;
    }



    .add-product-button {
        height: 36px !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        font-size: 0.875rem !important;
    }

        .add-product-button:hover {
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
        }

    .products-grid-section {
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
    }

    .enhanced-grid {
        background: white;
        border-radius: 12px;
        overflow: hidden;
    }

        .enhanced-grid .e-gridheader {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            font-weight: 600 !important;
        }

            .enhanced-grid .e-gridheader th {
                border: none !important;
                padding: 16px 12px !important;
                font-size: 0.875rem !important;
            }

        .enhanced-grid .e-row {
            border-bottom: 1px solid #f1f5f9 !important;
        }

            .enhanced-grid .e-row:hover {
                background: #f8fafc !important;
            }

        .enhanced-grid .e-rowcell {
            border: none !important;
            padding: 16px 12px !important;
            vertical-align: middle !important;
        }

    .product-name-cell {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
    }

    .product-cell-icon {
        color: var(--mud-palette-primary);
    }

    .byproduct-chip {
        border-radius: 16px !important;
        font-size: 0.75rem !important;
    }

    .remove-button {
        border-radius: 8px !important;
    }

        .remove-button:hover {
            background-color: rgba(244, 67, 54, 0.1) !important;
        }

    .empty-state {
        text-align: center;
        padding: 48px 24px;
    }

    .empty-icon {
        font-size: 4rem !important;
        color: #cbd5e1;
        margin-bottom: 16px;
    }

    .empty-title {
        color: var(--mud-palette-text-primary);
        margin-bottom: 8px !important;
        font-weight: 500 !important;
    }

    .empty-text, .empty-subtitle {
        color: var(--mud-palette-text-secondary);
        margin: 0 !important;
    }

    .empty-products-section {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: 2px dashed #cbd5e1;
    }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .security-settings-container

    {
        padding: 16px;
    }

    .section-card {
        padding: 16px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .add-product-section {
        padding: 16px;
    }

    .organization-selection-row,
    .product-selection-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .autocomplete-container,
    .product-select-container {
        width: 100%;
    }

    }

    /* Loading States */
    .mud-progress-circular {
        color: white !important;
    }

    /* Focus States */
    .mud-input-outlined:focus-within {
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }

    .mud-select:focus-within {
        box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    }
</style>