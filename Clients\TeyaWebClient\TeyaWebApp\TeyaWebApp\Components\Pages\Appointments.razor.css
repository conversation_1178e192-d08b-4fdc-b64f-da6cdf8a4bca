﻿.search-container {
    margin-bottom: 0.75rem;
}

.close-button {
    padding: 15px 80px;
    margin: 15px;
}

.search-group {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.search-criteria, .search-input {
    flex: 1;
}

.search-criteria {
    min-width: 120px;
}

.criteria-dropdown {
    width: 100%;
}

.input-button-group {
    display: flex;
    gap: 0.5rem;
}

    .input-button-group input {
        flex: 1;
        padding: 4px;
        border: 1px solid #ccc;
        border-radius: 4px;
        font-size: 0.85rem;
    }

.search-button {
    white-space: nowrap;
    font-size: 0.85rem;
    padding: 2px 8px;
}

/* Form scroll container */
.form-scroll-container {
    margin-top: 10px;
    max-height: 400px;
    overflow-y: auto;
    padding-right: 8px;
    margin-bottom: 0.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 12px;
}

/* Form row for side-by-side layout */
.form-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

/* Compact form fields */
.form-field-compact {
    flex: 1;
    margin-bottom: 0.5rem;
}

.form-field-full {
    width: 100%;
    margin-bottom: 0.5rem;
}

    .form-field-compact label,
    .form-field-full label {
        font-weight: bold;
        margin-bottom: 0.25rem;
        color: #333;
        font-size: 0.85rem;
        display: block;
    }

/* Compact form controls */
.form-control-dropdown-compact,
.form-control-date-compact,
.form-control-time-compact {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    font-size: 0.85rem;
    height: 32px;
}

.form-control-textarea-compact {
    width: 100%;
    min-height: 60px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    resize: vertical;
    font-family: inherit;
    font-size: 0.85rem;
}

    .form-control-dropdown-compact:focus,
    .form-control-date-compact:focus,
    .form-control-time-compact:focus,
    .form-control-textarea-compact:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.25);
        outline: 0;
    }

/* Compact submit button */
.submit-button-compact {
    min-width: 100px;
    font-size: 0.9rem;
    padding: 5px 15px;
}

/* Alert styles */
.alert {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 0.85rem;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Margin utilities */
.mt-2 {
    margin-top: 0.5rem;
}

.mt-3 {
    margin-top: 0.75rem;
}

/* Card styling */
.fixed-card {
    width: 700px;
    height: 500px;
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 0;
    display: none;
}

    .overlay.visible {
        display: block;
    }

.appointment-form {
    padding: 15px;
}

.btn-close {
    height: 1.75em;
    width: 1.75em;
}

.appointment-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #112442;
    margin: 5px;
    text-align: center;
    width: 100%;
}

.mt-3.d-flex.justify-content-center {
    display: flex;
    justify-content: center;
}

/* Custom scrollbar styling */
.form-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.form-scroll-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.form-scroll-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

    .form-scroll-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

/* Calendar editor styling */
.custom-event-editor {
    margin: 15px;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 94%;
}

.e-textlabel {
    font-weight: bold;
    margin-bottom: 5px;
}

.custom-event-editor td {
    padding: 10px;
}

.custom-event-editor input,
.custom-event-editor .e-textbox,
.custom-event-editor .e-datepicker,
.custom-event-editor .e-timepicker {
    width: 100%;
}

.e-grid .e-altrow {
    background-color: #fafafa;
}
