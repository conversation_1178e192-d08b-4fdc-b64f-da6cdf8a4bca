﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using static TeyaWebApp.Components.Pages.Notes;
using static MudBlazor.Icons.Custom;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Net.Http;
using Syncfusion.Blazor.DropDowns;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Graph.Models;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class VisionExamination : ComponentBase
    {
        private MudDialog _addMemberDialog;
        private string editorContent;
        private bool isDialogOpen = false;
        private VisionRx VisionData = new VisionRx();
        private List<VisionRx> VisionDataList = new();
        private List<TemplateData> templates = new List<TemplateData>();
        private string Diopter = "Diopter";
        public SfGrid<VisionRx> visionRxGrid { get; set; }
        private List<VisionRx> visionRecordsList;
        private Guid selectedExaminationId;
        private Guid PatientId { get; set; }
        private string MM = "MM";
        private SfRichTextEditor RichTextEditor;
        private bool add = false;
        private Guid organizationId { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ILogger<Config> Logger { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }
        private bool update = false;
        private int activeTabIndex = 0;
        private bool subscription = false;
        private bool isInternalUpdate { get; set; } = false;

        /// <summary>
        /// get list of Vision Examination Record from database
        /// </summary>
        protected override async Task OnInitializedAsync()
        {

                PatientId = PatientID;
                organizationId = OrgId;
                ManualContent = Data;
                subscription = UserContext.ActiveUserSubscription;
                VisionDataList = await VisionExaminationService.GetVisionExaminationByIdAsyncAndIsActive(PatientId, organizationId, subscription);
                editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);

        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }


        /// <summary>
        /// Update to RichTextEditor
        /// </summary>
        private void ViewHandler()
        {
            string str = "<ul style='list-style-type: none; padding-left: 0;'>"; 
            foreach (var member in VisionDataList)
            {
                str += $"<li><b>Created Date:</b> {member.CreatedDate}</li>";
                str += $"<li><b>Updated Date:</b> {member.UpdatedDate}</li>";
                str += "<br>";
            }
            str += "</ul>"; 
            editorContent = str;
        }

        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = string.Empty;
            if (VisionDataList != null && VisionDataList.Any())
            {
                dynamicContent = "<ul style='list-style-type: none; padding-left: 0;'>";
                foreach (var member in VisionDataList)
                {
                    dynamicContent += $"<li><b>Created Date:</b> {member.CreatedDate}</li>";
                    dynamicContent += $"<li><b>Updated Date:</b> {member.UpdatedDate}</li>";
                    dynamicContent += "<br>";
                }
                dynamicContent += "</ul>";
            }

            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }

        private async void OpenVisionScreeningTab()
        {
            VisionData = new VisionRx();
            update = false;
            selectedExaminationId = Guid.Empty;
            activeTabIndex = 1;
        }
        private void OnRowSelected(RowSelectEventArgs<VisionRx> args)
        {
            var selectedRecord = args.Data;
            update = true;
            
            if (selectedRecord != null)
            {
                VisionData = JsonSerializer.Deserialize<VisionRx>(
                    JsonSerializer.Serialize(selectedRecord)
                );
            selectedExaminationId = args.Data.ExaminationId;
            }
            activeTabIndex = 1;
        }

   

        /// <summary>
        /// To Handle click Outside the SF Grid
        /// </summary>
        private void HandleSelectionChange(string newValue)
        {
            VisionData.UnaidedSIUnit = newValue;
        }

        /// <summary>
        /// Navbar for RichTextEditor
        /// </summary>
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "add"},
            new ToolbarItemModel() { Name = "close" },
        };

        /// <summary>
        /// Add Vision Examination Record to database
        /// </summary>
        private async Task SaveChanges()
        {

            if (!update)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Save"],
                    @Localizer["Do you want to Save this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    return;
                }

                VisionData.PatientId = PatientId;
                VisionData.OrganizationId = organizationId;
                VisionData.CreatedDate = DateTime.Now;
                VisionData.UpdatedDate = DateTime.Now;
                VisionData.CreatedBy = Guid.Parse(User.id);
                VisionData.UpdatedBy = Guid.Parse(User.id);
                VisionData.IsActive = true;
                await VisionExaminationService.CreateVisionExaminationAsync(VisionData);
                
            }
            else
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Update"],
                    @Localizer["Do you want to Update this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    return;
                }

                VisionData.UpdatedDate = DateTime.Now;
                VisionData.CreatedBy = Guid.Parse(User.id);
                VisionData.UpdatedBy = Guid.Parse(User.id);
                await VisionExaminationService.UpdateVisionRecordsAsync(VisionData);
            }
            VisionDataList = await VisionExaminationService.GetVisionExaminationByIdAsyncAndIsActive(PatientId, organizationId, subscription);
            update = false;
            VisionData = new VisionRx();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            CloseDialog();
        }

        private void OnRowDataBound(RowDataBoundEventArgs<VisionRx> args)
        {
            if (args.Data.ExaminationId == selectedExaminationId)
            {
                args.Row.AddClass(new[] { "selected-row" });
            }
        }
        private async Task OnActionBegin(ActionEventArgs<VisionRx> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Paging)
            {
                var currentPage = visionRxGrid?.PageSettings?.CurrentPage ?? 1;
                await visionRxGrid?.GoToPageAsync(currentPage + 1);
                return;
            }
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to Delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

            if (result != true)
            {
                return;
            }
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedRecord = args.Data;
                if (deletedRecord is VisionRx recordToDelete)
                {
                    await VisionExaminationService.DeleteVisionRecordsAsync(recordToDelete.ExaminationId, organizationId, subscription);
                    VisionDataList = await VisionExaminationService.GetVisionExaminationByIdAsyncAndIsActive(PatientId, organizationId, subscription);
                    update = false;
                    VisionData = new VisionRx();
                    editorContent = GenerateRichTextContent(ManualContent);
                    await InvokeAsync(StateHasChanged);
                    CloseDialog();
                }
            }
        }


        /// <summary>
        /// Cancel all edit, delete and add operations and close the dialogBox
        /// </summary>
        private async Task CancelChanges()
        {
            VisionData = new VisionRx();
            update = false;
            await InvokeAsync(StateHasChanged);
            CloseDialog();
        }

        /// <summary>
        /// Open dialog
        /// </summary>
        private void OpenDialog()
        {
            activeTabIndex = 0;
            VisionData = new VisionRx();
            isDialogOpen = true;
            update = false;
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseDialog()
        {
            update = false;
            VisionData = new VisionRx();
            isDialogOpen = false;
        }
        private async void CancelAction()
        {
            _addMemberDialog.CloseAsync();
            await InvokeAsync(StateHasChanged);
            StateHasChanged();
        }

        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}