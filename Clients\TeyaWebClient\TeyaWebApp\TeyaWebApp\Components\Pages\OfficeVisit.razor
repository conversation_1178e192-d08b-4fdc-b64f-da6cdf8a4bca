@page "/OfficeVisit"

@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Syncfusion.Blazor
@using Syncfusion.Blazor.Grids
@using MudBlazor
@using TeyaWebApp.Components.Layout
@using TeyaUIModels.Model
@inject TeyaUIViewModels.ViewModel.IOfficeVisitService VisitService
@inject IMemberService MemberService
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@attribute [Authorize(Policy = "VisitAccessPolicy")]


@layout Admin
<PageTitle>@Localizer["Office Visits"]</PageTitle>

<div class="office-visit-wrapper">
    <div class="d-flex align-items-center mb-4">
        <MudIcon Icon="@Icons.Material.Filled.MedicalInformation" Size="Size.Medium" Class="me-2 text-primary" />
        <h3 class="m-0">Office Visit</h3>
    </div>
    <div class="visit-card sf-box">
        <SfGrid DataSource="@OfficeVisits" AllowPaging="true" GridLines="GridLine.Horizontal" AllowTextWrap="true"
                CssClass="custom-grid">

            <GridPageSettings PageSize="10" PageSizes="true" />

            <GridColumns>
                <GridColumn Field=@nameof(OfficeVisitModel.VisitType) HeaderText="@Localizer["Visit Type"]" Width="130" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.AppointmentTime) HeaderText="@Localizer["Appointment Time"]" Format="H:mm" Width="140" TextAlign="TextAlign.Center" />

                <GridColumn HeaderText="@Localizer["Patient Name"]" Width="150" TextAlign="TextAlign.Center">
                    <Template>
                        @{
                            var visit = (OfficeVisitModel)context;
                        }
                        @if (!string.IsNullOrEmpty(visit.PatientName))
                        {
                            <span class="clickable-name" @onclick="() => RedirectToChart(visit.Id, visit.VisitStatus, visit.VisitType)">
                                @visit.PatientName
                            </span>
                        }
                        else
                        {
                            <span class="placeholder-text">N/A</span>
                        }
                    </Template>
                </GridColumn>

                <GridColumn Field=@nameof(OfficeVisitModel.PR) HeaderText="@Localizer["P/R"]" Width="100" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.Reason) HeaderText="@Localizer["Reason"]" Width="120" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.Notes) HeaderText="@Localizer["Notes"]" Width="130" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.Sex) HeaderText="@Localizer["Sex"]" Width="90" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.Dob) HeaderText="@Localizer["Date of Birth"]" Format="MMM dd, yyyy" Width="130" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.VisitStatus) HeaderText="@Localizer["Visit Status"]" Width="140" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.ArrivalTime) HeaderText="@Localizer["Arrival Time"]" Format="H:mm" Width="120" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.Duration) HeaderText="@Localizer["Duration"]" Width="110" TextAlign="TextAlign.Center" />
                <GridColumn Field=@nameof(OfficeVisitModel.RoomNumber) HeaderText="@Localizer["Room Number"]" Width="130" TextAlign="TextAlign.Center" />
            </GridColumns>

        </SfGrid>
    </div>

</div>

<style>
    .office-visit-wrapper {
        padding: 1rem 1.5rem;
        background-color: #f9f9f9;
        min-height: 100vh;
    }

    .visit-card {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .custom-grid .e-gridheader {
        background-color: #f0f4f8;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .custom-grid .e-gridcontent {
        font-size: 0.9rem;
    }

    .clickable-name {
        color: var(--mud-palette-primary);
        font-weight: 500;
        cursor: pointer;
        
    }

    .placeholder-text {
        color: #999;
        font-style: italic;
    }

    .d-flex {
        display: flex;
    }

    .align-items-center {
        align-items: center;
    }

    .me-2 {
        margin-right: 0.5rem;
    }

    .mb-4 {
        margin-bottom: 1.5rem;
    }

    .text-primary {
        color: var(--mud-palette-primary);
    }

    @@media (max-width: 768px) {
        .visit-card {
            padding: 1rem;
        }

        .custom-grid .e-gridcontent,
        .custom-grid .e-gridheader {
            font-size: 0.85rem;
        }
    }
</style>