@page "/SurgicalHistory"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids
@using System

<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="Symbol">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                               OnClick="OpenNewDialogBox"
                                               Size="Size.Small" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>

<MudDialog @ref="_surgicalhistory" Style="width: 80vw; max-width: 1100px;" OnBackdropClick=" HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            @Localizer["SurgicalHistory"]
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; flex-direction: column;">
            <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
                <MudGrid Spacing="3" Style="align-items: center;">
                    <MudItem xs="3">
                        <MudSelect T="string"
                                   Label="@Localizer["Select Database"]"
                                   Value="selectedDatabase"
                                   ValueChanged="OnDatabaseChanged"
                                   Dense="true"
                                   Margin="Margin.Dense"
                                   Variant="Variant.Outlined"
                                   Style="width: 100%;">
                            <MudSelectItem T="string" Value="@("CMS")">@Localizer["CMS"]</MudSelectItem>
                            <MudSelectItem T="string" Value="@("FDB")">@Localizer["FDB"]</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                </MudGrid>


                <!-- Drug name search and Add button row -->
                <MudGrid Spacing="3" Style="align-items: center; margin-top: 8px;">
                    <MudItem xs="4">
                        <MudAutocomplete T="string"
                                         Label="@Localizer["Search ICD By Codes or Description"]"
                                         Value="ICDName"
                                         ValueChanged="OnICDNameChanged"
                                         SearchFunc="SearchICDCodes"
                                         ToStringFunc="@(s => s)"
                                         CoerceText="true"
                                         Clearable="true"
                                         Dense="true"
                                         ResetValueOnEmptyText="true"
                                         Variant="Variant.Outlined"
                                         Margin="Margin.Dense"
                                         MinCharacters="2"
                                         Style="width: 100%;" />
                    </MudItem>
                    <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                        <MudButton Color="Color.Primary"
                                   OnClick="AddNewSurgery"
                                   Variant="Variant.Filled"
                                   Dense="true"
                                   Style="min-width: 70px; height: 35px;">
                            @Localizer["Add"]
                        </MudButton>
                    </MudItem>
                </MudGrid>

                <SfGrid @ref="SurgeryGrid" TValue="Surgical"   Style="font-size: 0.85rem; margin-top: 24px;" DataSource="@surgicalhistory" AllowPaging="true" PageSettings-PageSize="5" GridLines="GridLine.Both">
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
                    <GridPageSettings PageSize="10"></GridPageSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="Surgical"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="SurgeryId" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Date"]" Width="15"
                                    TextAlign="TextAlign.Center" Format="MM/dd/yyyy">
                            <EditTemplate>
                                <SfDatePicker @bind-Value="@((context as Surgical).CreatedDate)"
                                              Max="@DateTime.Now"
                                              Placeholder="Select a date"
                                              ShowClearButton="false">
                                </SfDatePicker>
                            </EditTemplate>
                        </GridColumn>
                        <GridColumn Field="Surgery" HeaderText="@Localizer["SurgeryReason"]" Width="75" HeaderTextAlign="TextAlign.Center" TextAlign="TextAlign.Left"></GridColumn>
                        <GridColumn HeaderText="@Localizer["Actions"]" Width="10" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete",CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
                <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
                    <MudButton Color="Color.Secondary"
                               Variant="Variant.Outlined"
                               OnClick="CancelData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Cancel"]
                    </MudButton>
                    <MudButton Color="Color.Primary"
                               Variant="Variant.Filled"
                               OnClick="SaveData"
                               Dense="true"
                               Style="min-width: 120px; height: 40px; font-weight: 600;">
                        @Localizer["Save"]
                    </MudButton>
                </div>
            </div>
        </div>
    </DialogContent>
</MudDialog>


<style>
    ::deep .e-grid .e-headercell {
        border-right: 2px solid #c0c0c0 !important;
        border-bottom: 2px solid #c0c0c0 !important;
        padding: 8px !important;
        font-weight: bold;
    }

    ::deep .e-grid .e-rowcell {
        border-right: 2px solid #c0c0c0 !important;
        padding: 8px !important;
    }

    ::deep .e-grid .e-row {
        border-bottom: 2px solid #c0c0c0 !important;
    }

    ::deep .e-grid {
        border: 2px solid #c0c0c0 !important;
    }

        ::deep .e-grid .e-row:hover {
            background-color: #f5f5f5 !important;
        }
</style>


<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>