﻿@page "/Practice"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "PracticeAccessPolicy")]
@using Syncfusion.Blazor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Buttons
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Components.Layout
@layout Admin
@using Syncfusion.Blazor.Inputs
@inject ISnackbar Snackbar
@using Syncfusion.Blazor.Grids
@using TeyaWebApp.TeyaAIScribeResources
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@inject IPracticeService PracticeService
@inject IMemberService MemberService
@inject IOrganizationService OrganizationService

<div class="task-container" style="margin: 16px; padding: 16px; background-color: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <MudGrid>
        <MudItem xs="12" class="d-flex">
            <MudButton Variant="Variant.Filled"
                       Color="Color.Info" Size="Size.Small" OnClick="OpenAddTaskDialog">
                Add Task
            </MudButton>
        </MudItem>
    </MudGrid>

    <div class="search-input mb-2 p-3 mt-2" style="background-color: #ffffff; border: 1px solid #ddd; border-radius: 8px;">
        <label for="SearchProvider" class="mb-2 d-block font-weight-bold text-secondary" style="font-size: 14px; color: #333333;">@Localizer["SearchProvider:"]</label>
        <div class="input-button-group" style="position: relative; max-width: 250px;">
            <MudAutocomplete T="string"
                             Value="@selectedMember"
                             SearchFunc="SearchProvider"
                             ToStringFunc="@( (providerPatient) => providerPatient)"
                             Placeholder="@Localizer["Enter Provider Name"]"
                             ValueChanged="@OnProviderSelected"
                             Adornment="Adornment.End"
                             CoerceText="true"
                             Clearable="true"
                             Dense="true"
                             ResetValueOnEmptyText="true"
                             Margin="Margin.Dense"
                             Variant="Variant.Outlined"
                             MinCharacters="2">
            </MudAutocomplete>
        </div>
    </div>

    <SfGrid @ref="taskGrid" TValue="Tasks" DataSource="@filteredTasks" AllowPaging="true" OnCommandClick="OnGridCommandClick" GridLines="GridLine.Both">
        <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.Excel"></GridFilterSettings>
        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true"></GridEditSettings>
        <GridPageSettings PageSize="10"></GridPageSettings>
        <GridEvents OnActionBegin="ActionBeginHandler" TValue="Tasks">
        </GridEvents>
        <GridColumns>
            <GridColumn Field="@nameof(Tasks.PatientName)" HeaderText="@Localizer["PatientName"]" AllowEditing="false"></GridColumn>
            <GridColumn Field="@nameof(Tasks.DueDate)" HeaderText="@Localizer["DueDate"]" Format="MM/dd/yyyy"></GridColumn>
            <GridColumn Field="@nameof(Tasks.AssignedTo)" HeaderText="@Localizer["AssignedTo"]"></GridColumn>
            <GridColumn Field="@nameof(Tasks.Subject)" HeaderText="@Localizer["Subject"]"></GridColumn>
            <GridColumn Field="@nameof(Tasks.TaskType)" HeaderText="@Localizer["TaskType"]"></GridColumn>
            <GridColumn Field="@nameof(Tasks.Status)" HeaderText="@Localizer["Status"]"></GridColumn>
            <GridColumn HeaderText="Actions" Width="110" TextAlign="TextAlign.Center">
                <GridCommandColumns>
                    <GridColumn HeaderText="View" Width="90" TextAlign="TextAlign.Center">
                        <Template>
                            @{
                                var task = context as Tasks;
                            }
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                           Color="Color.Primary"
                                           OnClick="@(() => ShowTaskDialog(task))" />
                        </Template>
                    </GridColumn>
                    <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                    <GridCommandColumn Type="CommandButtonType.Save" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-update", CssClass = "e-flat" })" />
                    <GridCommandColumn Type="CommandButtonType.Cancel" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-cancel-icon", CssClass = "e-flat" })" />
                </GridCommandColumns>
            </GridColumn>
        </GridColumns>
    </SfGrid>
    <MudDialog Visible="isDialogOpen" Style="width: 85vw; max-width: 1000px;">
        <TitleContent>
            <MudText Typo="Typo.h6" >Task Details</MudText>
            <MudIconButton Icon="@Icons.Material.Filled.Close"
                           Color="Color.Inherit"
                           Size="Size.Small"
                           OnClick="CloseDialog"
                           Style="position: absolute; right: 16px; top: 6px;" />
        </TitleContent>

        <DialogContent>
            <MudPaper Style="background-color:#fafafa">
                <MudGrid Class="gap-y-2">
                    <MudItem xs="12" sm="6">
                        <MudText Typo="Typo.body2"><b>Patient Name:</b> @currentTask.PatientName</MudText>
                        <MudText Typo="Typo.body2"><b>SSN:</b> @currentTask.SSN</MudText>
                        <MudText Typo="Typo.body2"><b>Task Type:</b> @currentTask.TaskType</MudText>
                        <MudText Typo="Typo.body2"><b>Subject:</b> @currentTask.Subject</MudText>
                        <MudText Typo="Typo.body2"><b>Status:</b> @currentTask.Status</MudText>
                        <MudText Typo="Typo.body2"><b>Assigned To:</b> @currentTask.AssignedTo</MudText>
                        <MudText Typo="Typo.body2"><b>Priority:</b> @currentTask.Priority</MudText>
                        <MudText Typo="Typo.body2"><b>Created By:</b> @currentTask.CreatedBy</MudText>
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudText Typo="Typo.body2"><b>Creation Date:</b> @currentTask.CreationDate?.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.body2"><b>Start Date:</b> @currentTask.StartDate?.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.body2"><b>Due Date:</b> @currentTask.DueDate?.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.body2"><b>Last Due Date:</b> @currentTask.LastDueDate?.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.body2"><b>Last Visit Date:</b> @currentTask.LastVisitDate?.ToString("MM/dd/yyyy")</MudText>
                        <MudText Typo="Typo.body2"><b>Frequency:</b> @(currentTask.Frequency?.ToString() ?? "N/A")</MudText>
                        <MudText Typo="Typo.body2"><b>Recurring Action:</b> @(currentTask.RecurringAction == true ? "Yes" : "No")</MudText>
                    </MudItem>
                </MudGrid>

                <MudDivider Class="my-3" />

                <MudText Typo="Typo.body2" Class="mb-1"><b>Notes:</b></MudText>
                <MudPaper Class="p-2 bg-white text-black rounded elevation-1" Square="true">
                    @currentTask.Notes
                </MudPaper>
            </MudPaper>
        </DialogContent>

        <DialogActions>
            <div class="w-100 d-flex justify-end p-2">
                <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="CloseDialog">Close</MudButton>
            </div>
        </DialogActions>
    </MudDialog>


</div>

<MudDialog @ref="_addTaskDialog" MaxWidth="MaxWidth.Medium" Style="border: 1px solid grey; width: 600px; overflow: visible;">
    <TitleContent>
        <div style="display: flex; justify-content: space-between; align-items: center; width: 100%; padding: 12px 16px; background: #f5f5f5;">
            <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600; margin: 0;">
                @Localizer["ADD TASK"]
            </MudText>
            <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Medium" OnClick="CancelChanges" Style="margin: 0;" />
        </div>
    </TitleContent>
    <DialogContent>
        <div style="padding: 16px; display: flex; flex-direction: column;">
            <MudGrid Spacing="2" Style="display: flex; flex-wrap: wrap;">
                <MudItem xs="6">
                    <MudAutocomplete T="string"
                                     Value="@selectedPatientId"
                                     SearchFunc="SearchTasks"
                                     ToStringFunc="tasks => tasks"
                                     Placeholder="@Localizer["Enter Patient Name"]"
                                     ValueChanged="@OnTasksSelected"
                                     Adornment="Adornment.End"
                                     CoerceText="true"
                                     Clearable="true"
                                     Dense="true"
                                     ResetValueOnEmptyText="true"
                                     Margin="Margin.Dense"
                                     Variant="Variant.Outlined"
                                     MinCharacters="2"
                                     Style="height:30px;">
                    </MudAutocomplete>
                </MudItem>
                <MudItem xs="12"></MudItem>
                <MudItem xs="6">
                    <MudTextField Label="@Localizer["Task Type"].Value" @bind-Value="newTask.TaskType" />
                </MudItem>
                <MudItem xs="6">
                    <MudTextField Label="@Localizer["Subject"].Value" @bind-Value="newTask.Subject" />
                </MudItem>
                <MudItem xs="6">
                    <MudTextField label="@Localizer["Assigned To"].Value" @bind-Value="newTask.AssignedTo" />
                </MudItem>
                <MudItem xs="6">
                    <MudTextField label="@Localizer["Status"].Value" @bind-Value="newTask.Status" />
                </MudItem>
                <MudItem xs="6">
                    <MudTextField Label="@Localizer["Created By"]" @bind-Value="newTask.CreatedBy" ReadOnly="true" />

                </MudItem>
                <MudItem xs="6">
                    <MudTextField Label="@Localizer["Priority"].Value" @bind-Value="newTask.Priority" />
                </MudItem>
                <MudItem xs="6">
                    <MudTextField Label="@Localizer["Notes"].Value" @bind-Value="newTask.Notes" />
                </MudItem>
                <MudItem xs="6">
                    <MudSelect T="bool?" Label="@Localizer["Select Recurring Action"].Value"
                               @bind-Value="@newTask.RecurringAction"
                               Style="width: 100%;">
                        @foreach (var action in recurringActions)
                        {
                            <MudSelectItem T="bool?" Value="@action.Name">@action.Code</MudSelectItem>
                        }
                    </MudSelect>
                </MudItem>

                <MudItem xs="6">

                    <MudNumericField Label="@Localizer["Frequency"].Value" Min="1" @bind-Value="newTask.Frequency" />
                </MudItem>
                <MudItem xs="6">
                    <MudDatePicker Label="@Localizer["Creation Date"].Value" PickerVariant="PickerVariant.Dialog" @bind-Date="newTask.CreationDate" />
                </MudItem>
                <MudItem xs="6">
                    <MudDatePicker label="@Localizer["Start Date"].Value" PickerVariant="PickerVariant.Dialog" @bind-Date="newTask.StartDate" />
                </MudItem>
                <MudItem xs="6">
                    <MudDatePicker label="@Localizer["Due Date"].Value" PickerVariant="PickerVariant.Dialog" @bind-Date="newTask.DueDate" />
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudDatePicker label="@Localizer["Last Due Date"].Value" PickerVariant="PickerVariant.Dialog" @bind-Date="newTask.LastDueDate" />
                </MudItem>
                <MudItem xs="6">
                    <MudDatePicker label="@Localizer["Last Visit Date"].Value" PickerVariant="PickerVariant.Dialog" @bind-Date="newTask.LastVisitDate" />

                </MudItem>
            </MudGrid>
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 18px 24px; border-top: 1px solid #E0E0E0;">
            <MudButton Color="Color.Secondary"
                       Variant="Variant.Outlined"
                       OnClick="CancelChanges"
                       Dense="true"
                       Style="min-width: 80px; height: 35px; font-weight: 500;">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Color="Color.Primary"
                       Variant="Variant.Filled"
                       OnClick="SaveTaskAsync"
                       Dense="true"
                       Style="min-width: 80px; height: 35px; font-weight: 500;">
                @Localizer["Save"]
            </MudButton>
        </div>
    </DialogContent>
</MudDialog>

@code {

    
}