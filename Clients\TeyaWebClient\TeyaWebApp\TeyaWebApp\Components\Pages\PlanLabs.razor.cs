﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using Microsoft.Azure.Amqp.Framing;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Text;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class PlanLabs
    {
        [Inject]
        private ILogger<PlanLabs> _logger { get; set; }

        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] public ILabTestsService _labTestsService { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IMeasureService MeasureService { get; set; }
        [Inject] private SharedNotesService SharedNotesService { get; set; }
        [Inject] private IAssessmentsService assessmentsService { get; set; }
        [Inject] private IAlertService AlertService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] private ICustomLabAlertService CustomLabAlertService { get; set; }
        private MudDialog _planLabs;
        private DateTime? _CreatedDate = DateTime.Now;
        private DateTime? _UpdatedDate = DateTime.Now;
        private static readonly char[] SplitChars = { ' ', ',', '-', '(', ')', '/' };
        private List<LabTests> planlabs { get; set; } = new List<LabTests>();
        private SfRichTextEditor RichTextEditor;
        public SfGrid<LabTests> PlanLabsGrid { get; set; }

        private Guid? OrgID { get; set; }
        public Guid PatientId { get; set; }
        public Guid Id { get; set; }
        private string editorContent;
        public List<LabTests> deleteList = new List<LabTests>();
        public List<LabTests> AddList = new List<LabTests>();
        public List<AssessmentsData> Localdata { get; set; } = new List<AssessmentsData>();
        private List<string> AssessmentDiagnosis = new List<string>();

        [Inject] private IDialogService DialogService { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }

        //private string? _Description = "Enter Additional Reviews";
        private Patient _PatientData = new Patient();
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            _PatientData = _PatientService.PatientData;
           
            OrgID = OrgId;
            editorContent = TotalText;
            Localdata = (await assessmentsService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, false))
                .GroupBy(a=>a.Diagnosis)
                .Select(g => g.OrderByDescending(a => a.CreatedDate).First())
                .ToList();
            AssessmentDiagnosis = Localdata.Select(a => a.Diagnosis).ToList();
            SharedNotesService.OnChange += UpdateAssessments;
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            editorContent = GenerateRichTextContent(Data);
            await HandleDynamicComponentUpdate();

        }
        private void UpdateAssessments()
        {
            OnInitializedAsync();
            StateHasChanged();
        }
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" }
        };

        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        private RenderFragment<object> AssessmentEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests Labs) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", AssessmentDiagnosis);
            builder.AddAttribute(2, "Value", Labs.AssessmentData);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    Labs.AssessmentData = value;
                    var selectedAssessment = Localdata.FirstOrDefault(a => a.Diagnosis == value);
                    if (selectedAssessment != null)
                    {
                        Labs.AssessmentId = selectedAssessment.AssessmentsID;
                        Console.WriteLine(Labs.AssessmentId);
                    }
                }));
            builder.AddAttribute(4, "Placeholder", "Select Assessments");
            builder.CloseComponent();
        };
        private List<string> OrganizationOptions => new List<string>
        {
                Localizer["Quest Inc"].Value,
                Localizer["Lab Corp"].Value
        };
        private RenderFragment<object> OrganizationEditTemplate => (context) => (builder) =>
        {
            if (context is not LabTests labTest) return;

            builder.OpenComponent<SfDropDownList<string, string>>(0);
            builder.AddAttribute(1, "DataSource", OrganizationOptions);
            builder.AddAttribute(2, "Value", labTest.TestOrganization);
            builder.AddAttribute(3, "ValueChanged",
                EventCallback.Factory.Create<string>(this, value =>
                {
                    labTest.TestOrganization = value;
                }));
            builder.AddAttribute(4, "Placeholder", "Select Organization");
            builder.CloseComponent();
        };
        private async Task OpenNewDialogBox()
        {
            await _planLabs.ShowAsync();
        }
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _planLabs.CloseAsync();
        }
      
        private void ResetInputFields()
        {
            _CreatedDate = null;
        }
        public async Task ActionCompletedHandler(ActionEventArgs<LabTests> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleteLabTest = args.Data as LabTests;
                var existingItem = AddList.FirstOrDefault(m => m.LabTestsId == deleteLabTest.LabTestsId);
                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
            else if(args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                var newHistory = new LabTests
                {
                    LabTestsId = Guid.NewGuid(),
                    PatientId = PatientId,
                    PcpId = PatientId,
                    OrganizationId = OrgId,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    IsActive = true,
                };

                AddList.Add(newHistory);
                planlabs.Add(newHistory);
                await PlanLabsGrid.Refresh();
                ResetInputFields();

            }
        }
        public async Task ActionBeginHandler(ActionEventArgs<LabTests> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
                var today = DateTime.Now.Date;
                if (args.Data.CreatedDate > today)
                {
                    Snackbar.Add(Localizer["ValidateCreateDate"], Severity.Error);
                    args.Cancel = true;
                }
            }

           
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                
            }
        }
        private async Task CancelData()
        {
            deleteList.Clear();
            AddList.Clear();
            planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
            ResetInputFields();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }
        private async Task SaveData()
        {
            // Create a copy of AddList for alert checking
            var alertsCheckList = new List<LabTests>(AddList);

            try
            {
                // Validation checks
                if (AddList.Any(labTest => string.IsNullOrWhiteSpace(labTest.AssessmentData)))
                {
                    SnackBar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }
                if (planlabs.Any(labTest => string.IsNullOrWhiteSpace(labTest.AssessmentData)))
                {
                    SnackBar.Add(Localizer["Related Assessments is Blank"], Severity.Warning);
                    return;
                }

                // Check for alerts and show them in snackbar only, but don't save to database yet
                bool hasUnsafeLabTests = await CheckLabTestsForAlerts(alertsCheckList, showInSnackbarOnly: true);

                if (hasUnsafeLabTests)
                {
                    bool? confirmSave = await DialogService.ShowMessageBox(
                        Localizer["Safety Alert"],
                        Localizer["One or more lab tests may not be appropriate with the selected assessments. Do you want to save anyway?"],
                        yesText: Localizer["Save Anyway"],
                        noText: Localizer["Cancel"]);

                    if (confirmSave != true)
                    {
                        return; // Don't save if user cancels
                    }
                }

                // Save the data
                if (AddList.Count != 0)
                {
                    await _labTestsService.AddLabTestsAsync(AddList);
                    AddList.Clear();
                }

                await _labTestsService.UpdateLabTestsListAsync(planlabs);
                await _labTestsService.UpdateLabTestsListAsync(deleteList);
                deleteList.Clear();

                planlabs = await _labTestsService.GetAllByIdAndIsActiveAsync(PatientId);
                editorContent = GenerateRichTextContent(Data);
                await HandleDynamicComponentUpdate();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                await InvokeAsync(StateHasChanged);
                CloseNewDialogBox();

                // Now save alerts to database since user confirmed to save
                if (hasUnsafeLabTests)
                {
                    await CheckLabTestsForAlerts(alertsCheckList, showInSnackbarOnly: false);
                }

              
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving lab tests");
                SnackBar.Add(Localizer["Error saving changes"], Severity.Error);
            }
        }

        private async Task<bool> CheckLabTestsForAlerts(List<LabTests> testsToCheck, bool showInSnackbarOnly = false)
        {
            if (testsToCheck == null || !testsToCheck.Any())
                return false;

            int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string patientGender = _PatientData.Sex ?? "Unknown";
            var alertsToAdd = new List<Alert>();

            var customLabAlerts = await GetCustomLabAlertsForTests(testsToCheck);

            if (customLabAlerts.Count > 0)
            {
                foreach (var customAlert in customLabAlerts)
                {
                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = _PatientData.Id,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = _PatientData.OrganizationID ?? Guid.Empty,
                        Severity = "Not Configured",
                        AlertType = "Configured Lab Alert",
                        Description = customAlert.Description ?? $"Custom alert for {customAlert.Name}",
                        Solution = $"Follow the guidelines for {customAlert.Name}. See reference: {customAlert.WebReference}",
                        AdditionalInfo = $"Order Set: {customAlert.OrderSet}, Age Range: {customAlert.AgeLowerBound}-{customAlert.AgeUpperBound}, Gender: {customAlert.Gender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    SnackBar.Add($"[CONFIGURED ALERT] {customAlert.Name} - {customAlert.Description}",
                                Severity.Info,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.MedicalServices;
                                });
                }
            }

            foreach (var test in testsToCheck)
            {
                if (string.IsNullOrEmpty(test.AssessmentData) || string.IsNullOrEmpty(test.LabTest1))
                    continue;

                var assessment = Localdata.FirstOrDefault(a => a.AssessmentsID == test.AssessmentId);
                if (assessment == null || string.IsNullOrEmpty(assessment.Diagnosis))
                    continue;

                bool isMatch = await CheckTestMatchesAssessment(test.LabTest1, assessment.Diagnosis);

                if (!isMatch)
                {
                    string severityResponse = await GetTestSeverity(test.LabTest1, assessment.Diagnosis);
                    string severityLevel = ExtractSeverityLevel(severityResponse);

                    var alert = new Alert
                    {
                        AlertId = Guid.NewGuid(),
                        PatientId = _PatientData.Id,
                        PatientName = _PatientData.Name ?? "Unknown",
                        OrganizationId = _PatientData.OrganizationID ?? Guid.Empty,
                        Severity = severityLevel,
                        AlertType = "AI Suggested Alert",
                        Description = $"The lab test '{test.LabTest1}' may not be appropriate for diagnosis '{assessment.Diagnosis}'.",
                        Solution = $"Consider reviewing the lab test order or consulting with a specialist. {severityResponse}",
                        AdditionalInfo = $"Test: {test.LabTest1}, Diagnosis: {assessment.Diagnosis}, Patient Age: {patientAge}, Gender: {patientGender}",
                        CreatedDate = DateTime.Now,
                        IsActive = true
                    };

                    alertsToAdd.Add(alert);

                    var snackbarSeverity = severityLevel switch
                    {
                        "High" => Severity.Error,
                        "Medium" => Severity.Warning,
                        "Low" => Severity.Info,
                        _ => Severity.Warning
                    };

                    SnackBar.Add($"[AI SUGGESTED] ({patientAge}y, {patientGender}): {test.LabTest1} may not be appropriate for {assessment.Diagnosis}. Severity: {severityLevel}",
                                snackbarSeverity,
                                config => {
                                    config.VisibleStateDuration = 10000;
                                    config.Icon = Icons.Material.Filled.SmartToy;
                                });
                }
            }

            // Only save alerts to database if showInSnackbarOnly is false
            if (alertsToAdd.Count > 0 && !showInSnackbarOnly)
            {
                try
                {
                    await AlertService.AddAlertsAsync(alertsToAdd, _PatientData.OrganizationID, false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, Localizer["ErrorAddingAlerts"]);
                }
            }

            return alertsToAdd.Count > 0;
        }
        private async Task<List<CustomLabAlerts>> GetCustomLabAlertsForTests(List<LabTests> testsToCheck)
        {
            try
            {
                var customLabAlerts = await CustomLabAlertService.GetActiveCustomLabAlertsByOrganizationIdAsync(Id, OrgId, false);
                if (customLabAlerts == null || customLabAlerts.Count == 0)
                    return new List<CustomLabAlerts>();

                int patientAge = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
                string patientGender = _PatientData.Sex ?? "Unknown";

                var matchingAlerts = new List<CustomLabAlerts>();

                var testDetails = new List<(string TestName, string Assessment)>();
                foreach (var test in testsToCheck)
                {
                    if (string.IsNullOrEmpty(test.AssessmentData) || string.IsNullOrEmpty(test.LabTest1))
                        continue;

                    var assessment = Localdata.FirstOrDefault(a => a.AssessmentsID == test.AssessmentId);
                    if (assessment == null || string.IsNullOrEmpty(assessment.Diagnosis))
                        continue;

                    testDetails.Add((test.LabTest1, assessment.Diagnosis));

                    if (!string.IsNullOrEmpty(test.LabTest2))
                        testDetails.Add((test.LabTest2, assessment.Diagnosis));
                }

                if (testDetails.Count == 0)
                    return matchingAlerts;

                var testNames = testDetails.Select(t => t.TestName).ToList();

                StringBuilder alertsDescription = new StringBuilder();
                for (int i = 0; i < customLabAlerts.Count; i++)
                {
                    var alert = customLabAlerts[i];
                    alertsDescription.AppendLine($"Alert {i + 1}:");
                    alertsDescription.AppendLine($"- Name: {alert.Name}");
                    alertsDescription.AppendLine($"- Description: {alert.Description}");
                    alertsDescription.AppendLine($"- Order Set: {alert.OrderSet}");
                    alertsDescription.AppendLine($"- Age Range: {(alert.AgeLowerBound.HasValue ? alert.AgeLowerBound.Value.ToString() : "Any")} to {(alert.AgeUpperBound.HasValue ? alert.AgeUpperBound.Value.ToString() : "Any")}");
                    alertsDescription.AppendLine($"- Gender: {alert.Gender ?? "Any"}");
                    alertsDescription.AppendLine();
                }

                StringBuilder testAssessmentInfo = new StringBuilder();
                foreach (var detail in testDetails)
                {
                    testAssessmentInfo.AppendLine($"- {detail.TestName} for {detail.Assessment}");
                }

                StringBuilder alertsSummary = new StringBuilder();
                for (int i = 0; i < customLabAlerts.Count; i++)
                {
                    var alert = customLabAlerts[i];
                    alertsSummary.AppendLine($"Alert {i + 1}: {alert.Name} - {alert.Description}");
                }

                string prompt = $"Determine which alerts apply to these test-diagnosis pairs:\n\n" +
                    $"Patient: {patientAge}y, {patientGender}\n" +
                    $"Test-Diagnosis Pairs:\n{testAssessmentInfo}\n\n" +
                    $"Alerts:\n{alertsSummary}\n" +
                    $"Return comma-separated alert numbers that apply (e.g., '1,3,5'). If none apply, return 'None'.";


                // Ask GPT
                string response = await AskGptModel(prompt);

                if (!response.Trim().Equals("None", StringComparison.OrdinalIgnoreCase))
                {
                    var matches = Regex.Matches(response, @"\d+");
                    foreach (Match match in matches)
                    {
                        if (int.TryParse(match.Value, out int alertIndex) &&
                            alertIndex >= 1 &&
                            alertIndex <= customLabAlerts.Count)
                        {
                            matchingAlerts.Add(customLabAlerts[alertIndex - 1]);
                        }
                    }
                }

                if (matchingAlerts.Count == 0)
                {
                    foreach (var alert in customLabAlerts)
                    {
                        bool testMatch = false;

                        if (!string.IsNullOrEmpty(alert.Name))
                        {
                            foreach (var testName in testNames)
                            {
                                if (alert.Name.Contains(testName, StringComparison.OrdinalIgnoreCase))
                                {
                                    testMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!testMatch && !string.IsNullOrEmpty(alert.Description))
                        {
                            foreach (var testName in testNames)
                            {
                                if (alert.Description.Contains(testName, StringComparison.OrdinalIgnoreCase))
                                {
                                    testMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!testMatch && !string.IsNullOrEmpty(alert.OrderSet))
                        {
                            foreach (var testName in testNames)
                            {
                                if (alert.OrderSet.Contains(testName, StringComparison.OrdinalIgnoreCase))
                                {
                                    testMatch = true;
                                    break;
                                }
                            }
                        }

                        if (!testMatch)
                        {
                            var testKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                            foreach (var test in testNames)
                            {
                                var words = test.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2) 
                                        testKeywords.Add(word);
                                }
                            }

                            var alertKeywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

                            if (!string.IsNullOrEmpty(alert.Name))
                            {
                                var words = alert.Name.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                    {
                                        alertKeywords.Add(word);
                                    }
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.Description))
                            {
                                var words = alert.Description.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2) 
                                        alertKeywords.Add(word);
                                }
                            }

                            if (!string.IsNullOrEmpty(alert.OrderSet))
                            {
                                var words = alert.OrderSet.Split(SplitChars, StringSplitOptions.RemoveEmptyEntries);
                                foreach (var word in words)
                                {
                                    if (word.Length > 2)
                                    {
                                        alertKeywords.Add(word);
                                    }
                                }
                            }

                            foreach (var testKeyword in testKeywords)
                            {
                                if (alertKeywords.Contains(testKeyword))
                                {
                                    testMatch = true;
                                    break;
                                }
                            }
                        }

                        if (testMatch)
                        {
                            bool ageMatch = true;
                            if (alert.AgeLowerBound.HasValue && patientAge < alert.AgeLowerBound.Value)
                                ageMatch = false;
                            if (alert.AgeUpperBound.HasValue && patientAge > alert.AgeUpperBound.Value)
                                ageMatch = false;

                            bool genderMatch = true;
                            if (!string.IsNullOrEmpty(alert.Gender) && alert.Gender != "Both")
                            {
                                if (_PatientData.Sex != alert.Gender)
                                    genderMatch = false;
                            }

                            if (ageMatch && genderMatch)
                            {
                                matchingAlerts.Add(alert);
                            }
                        }
                    }
                }

                return matchingAlerts;
            }
            catch (Exception ex)
            {
                return new List<CustomLabAlerts>();
            }
        }
        private static string ExtractSeverityLevel(string response)
        {
            response = response.ToLower();

            if (response.Contains("critical") || response.Contains("severe") || response.Contains("high"))
                return "High";
            else if (response.Contains("medium") || response.Contains("moderate"))
                return "Medium";
            else if (response.Contains("low") || response.Contains("minor"))
                return "Low";
            else
                return "Medium";
        }
        private async Task<bool> CheckTestMatchesAssessment(string testName, string assessmentDiagnosis)
        {
            // Include patient data in the prompt
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string prompt = $"Does the lab test '{testName}' match with the assessment diagnosis '{assessmentDiagnosis}' for a {age}-year-old {gender} patient? Answer only with 'yes' or 'no'.";
            string response = await AskGptModel(prompt);

            return response.Trim().ToLower().Contains("yes", StringComparison.OrdinalIgnoreCase);
        }
        private async Task<string> GetTestSeverity(string testName, string assessmentDiagnosis)
        {
            int age = _PatientData.DOB.HasValue ? (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";
            string patientName = _PatientData.Name ?? "Unknown";

            string prompt = $"What is the severity level (Low, Medium, High) of prescribing lab test '{testName}' for a {age}-year-old {gender} patient named {patientName} with diagnosis '{assessmentDiagnosis}'? Consider age and gender-specific factors in your assessment. Explain briefly why.";
            string response = await AskGptModel(prompt);

            return response.Trim();
        }
        private async Task<string> AskGptModel(string prompt)
        {
            int age = _PatientData.DOB.HasValue ?
                      (int)((DateTime.Now - _PatientData.DOB.Value).TotalDays / 365.25) : 0;
            string gender = _PatientData.Sex ?? "Unknown";

            string systemMessage = $"You are a medical assistant helping to evaluate the appropriateness of lab tests for specific diagnoses. " +
                                   $"The patient is {age} years old and {gender}. " +
                                   $"Consider age-appropriate and gender-specific medical considerations in your evaluation. " +
                                   $"Provide concise, accurate information that takes into account the patient's demographic factors.";
          
             return await MeasureService.AskGptAsync(systemMessage, prompt);
        }


        private string GenerateRichTextContent(string manualData)
        {
            manualData ??= string.Empty;

            // Manual content section
            string manualSection = $@"<h4 style='margin-top: 20px; margin-bottom: 10px;'>Manual Content</h4>
    {manualData}";

            // Dynamic content section for planlabs 
            string dynamicContent = (planlabs != null && planlabs.Any())
                ? string.Join(" ",
                    planlabs.OrderByDescending(m => m.CreatedDate)
                        .Select(m =>
                            $"<ul><li style='margin-left: 20px;'> <b>{(m.CreatedDate.HasValue ? m.CreatedDate.Value.ToShortDateString() : Localizer["NoDate"])}: </b> " +
                            $"<b>Lab Test 1 :</b> {(string.IsNullOrWhiteSpace(m.LabTest1) ? Localizer["NoValue"] : m.LabTest1)} , " +
                            $"<b>Lab Test 2 :</b> {(string.IsNullOrWhiteSpace(m.LabTest2) ? Localizer["NoValue"] : m.LabTest2)} , " +
                            $"<b>Test Organization :</b> {(string.IsNullOrWhiteSpace(m.TestOrganization) ? Localizer["NoValue"] : m.TestOrganization)}</li></ul>."))
                : "<ul><li style='margin-left: 20px;'>No lab data available.</li></ul>";

            return $@"<div>
        {manualSection}
        <h4 style='margin-bottom: 10px;'>Dynamic Content</h4>
        {dynamicContent}
    </div>";
        }

        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(Data);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}