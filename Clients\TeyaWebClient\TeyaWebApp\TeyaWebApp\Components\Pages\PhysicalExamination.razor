﻿@page "/PhysicalExamination"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids


<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="Symbol">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline" OnClick="NewDialogBox" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>

<MudDialog @ref="_physicalExamination" Style="width:85vw; max-width:1200px;" OnBackdropClick=" HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size:1rem; font-weight:600;">@Localizer["Physical Examination"]</MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelData" Style="margin:-4px; position:absolute; right:16px; top:16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin:-12px; display:flex; flex-direction:column;">
            <div style="padding:20px; flex-grow:1; background-color:#ffffff;">
                <SfGrid @ref="PhysicalExaminationGrid"
                        TValue="Physicalexamination"
                        DataSource="@physicalexamination"
                        Toolbar="@(new List<string>() { @Localizer["Add"] })"
                        AllowPaging="true"
                        Style="font-size:0.85rem;" GridLines="GridLine.Both">
                    <GridPageSettings PageSize="5"></GridPageSettings>
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" AllowEditOnDblClick="true"></GridEditSettings>
                    <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="Physicalexamination"></GridEvents>
                    <GridColumns>
                        <GridColumn Field="ExaminationId" IsPrimaryKey="true" Visible="false" />
                        <GridColumn Field="CreatedDate" HeaderText="@Localizer["Date"]" TextAlign="TextAlign.Center" AllowEditing="true" Width="30" Format="MM/dd/y" />
                        <GridColumn Field="Skin" HeaderText="@Localizer["Skin"]" TextAlign="TextAlign.Center" Width="30" />
                        <GridColumn Field="Rash" HeaderText="@Localizer["Rash"]" TextAlign="TextAlign.Center" Width="30" />
                        <GridColumn Field="Tester" HeaderText="@Localizer["Tester"]" TextAlign="TextAlign.Center" Width="25" />
                        <GridColumn Field="Moles" HeaderText="@Localizer["Moles"]" TextAlign="TextAlign.Center" Width="30" />
                        <GridColumn Field="Hemangioma" HeaderText="@Localizer["Hemangioma"]" TextAlign="TextAlign.Center" Width="25" />
                        <GridColumn Field="VascularMalformation" HeaderText="@Localizer["Vascular Malformation"]" TextAlign="TextAlign.Center" Width="40" />
                        <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="25">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete"
                                                   ButtonOption="@(new CommandButtonOptions(){ IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>
            </div>
            <div style="display:flex; justify-content:flex-end; gap:12px; padding:16px 24px; border-top:1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary" Variant="Variant.Outlined" OnClick="CancelData" Dense="true" Style="min-width:120px; height:40px; font-weight:600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveData" Dense="true" Style="min-width:120px; height:40px; font-weight:600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>


<style>
    ::deep .e-grid .e-headercell {
        border-right: 2px solid #c0c0c0 !important;
        border-bottom: 2px solid #c0c0c0 !important;
        padding: 8px !important;
        font-weight: bold;
    }

    ::deep .e-grid .e-rowcell {
        border-right: 2px solid #c0c0c0 !important;
        padding: 8px !important;
    }

    ::deep .e-grid .e-row {
        border-bottom: 2px solid #c0c0c0 !important;
    }

    ::deep .e-grid {
        border: 2px solid #c0c0c0 !important;
    }

        ::deep .e-grid .e-row:hover {
            background-color: #f5f5f5 !important;
        }
</style>

<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>
