﻿@page "/license"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "licenseAccessPolicy")]
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject IDialogService DialogService


<GenericCard Heading="@Localizer["User License Management"]">
    <MudPaper Elevation="2">
        <MudGrid Spacing="0">
            <MudItem xs="12" sm="6" md="4" lg="3">
                <MudSelect Label="Organization" @bind-Value="selectedOrganization" Required="true" Position="Position.Relative">
                    <MudSelectItem Value="Guid.Empty" Disabled="true">-- Select Organization --</MudSelectItem>
                    @foreach (var org in ActiveOrganizations)
                    {
                        <MudSelectItem Value="@org.OrganizationId">@org.OrganizationName</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
        </MudGrid>
    </MudPaper>
    <MudDivider />
    <SfGrid DataSource="@ActiveUserLicenses" Toolbar="@ToolBarItems" @ref="licenseGrid"
            AllowPaging="true" AllowSorting="true" GridLines="GridLine.Both">
        <GridEvents OnActionBegin="OnActionBegin"
                    OnActionComplete="OnActionComplete" TValue="UserLicense" />
        <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal" />
        <GridColumns>
            <GridColumn Field="Id" HeaderText="ID" IsPrimaryKey="true" TextAlign="TextAlign.Center" Width="300" />

            <GridForeignColumn TValue="PlanType" Field="PlanId" HeaderText="Plan Name" ForeignKeyField="Id"
                               ForeignKeyValue="PlanName" ForeignDataSource="PlanList" TextAlign="TextAlign.Center" Width="150" />

            <GridForeignColumn TValue="Organization" Field="OrganizationId" AllowEditing="false" HeaderText="Organization Name" ForeignKeyField="OrganizationId"
                               ForeignKeyValue="OrganizationName" ForeignDataSource="OrganizationList" TextAlign="TextAlign.Center" Width="150" />

            <GridForeignColumn TValue="Product" Field="ProductId" HeaderText="Product Name" ForeignKeyField="Id"
                               ForeignKeyValue="Name" ForeignDataSource="ProductList" TextAlign="TextAlign.Center" Width="150" />

            <GridColumn Field="Seats" HeaderText="Seats" TextAlign="TextAlign.Center" Width="150" />
            <GridColumn Field="CreatedDate" HeaderText="Created Date" AllowEditing="false" Format="d" Width="120" />
            <GridColumn Field="UpdatedDate" HeaderText="Updated Date" AllowEditing="false" Format="d" Width="120" />
            <GridColumn Field="CreatedBy" HeaderText="Created By" AllowEditing="false" Width="220">
                <Template>
                    @{
                        var createdById = (context as UserLicense)?.CreatedBy;
                        var createdByName = GetUserDisplayName(createdById);
                    }
                    @createdByName
                </Template>
            </GridColumn>

            <GridColumn Field="UpdatedBy" HeaderText="Updated By" AllowEditing="false" Width="220">
                <Template>
                    @{
                        var updatedById = (context as UserLicense)?.UpdatedBy;
                        var updatedByName = GetUserDisplayName(updatedById);
                    }
                    @updatedByName
                </Template>
            </GridColumn>
            <GridColumn Field="Status" HeaderText="Status" Width="100">
                <Template>
                    @{
                        var statusText = (context as UserLicense)?.Status == true ? "Active" : "Inactive";
                    }
                    @statusText
                </Template>
            </GridColumn>

            <GridColumn Field="ExpiryDate" HeaderText="Expiry Date" Format="d" Width="120" />
        </GridColumns>
    </SfGrid>
</GenericCard>