﻿@using Microsoft.AspNetCore.Components
@using Microsoft.Extensions.Logging
@using MudBlazor
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Grids
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<div style="display: flex; flex-direction: column;">
    <div style="padding: 20px; flex-grow: 1; background-color: #ffffff;">
        <MudGrid Spacing="3" Style="align-items: center;">
            <MudItem xs="4">
                <MudAutocomplete T="string"
                Label="@Localizer["Search Brand Names"]"
                Value="@drugName"
                ValueChanged="@(EventCallback.Factory.Create<string>(this, OnDrugNameChanged))"
                SearchFunc="@SearchBrandNames"
                ToStringFunc="@(s => s)"
                CoerceText="true"
                Clearable="true"
                Dense="true"
                ResetValueOnEmptyText="true"
                Margin="Margin.Dense"
                Variant="Variant.Outlined"
                MinCharacters="3"
                Style="width: 100%;" />
            </MudItem>
            <MudItem xs="4">
                <MudSelect T="string"
                Label="@Localizer["Dosage & InTake"]"
                @bind-Value="finalSBD"
                Dense="true"
                Margin="Margin.Dense"
                Variant="Variant.Outlined"
                Style="width: 100%;">
                    @foreach (var drugs in BrandSBD)
                    {
                        <MudSelectItem T="string" Value="@drugs">@drugs</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="4" Style="display: flex; justify-content: flex-start; align-items: center;">
                <MudButton Color="Color.Primary"
                OnClick="@(EventCallback.Factory.Create(this, AddNewMedication))"
                Variant="Variant.Filled"
                Dense="true"
                Style="min-width: 100px; height: 40px;">
                    @Localizer["Add"]
                </MudButton>
            </MudItem>
        </MudGrid>

        <SfGrid @ref="MedicinesGrid"
        TValue="ActiveMedication"
        Style="font-size: 0.85rem; margin-top: 24px;"
                DataSource="@medicationRelatedToAssessments"
        AllowPaging="true"
        PageSettings-PageSize="5">
            <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>
            <GridPageSettings PageSize="10"></GridPageSettings>
            <GridEvents TValue="ActiveMedication"
            OnActionComplete="@(EventCallback.Factory.Create<Syncfusion.Blazor.Grids.ActionEventArgs<ActiveMedication>>(this, ActionCompletedHandler))"
            OnActionBegin="@(EventCallback.Factory.Create<Syncfusion.Blazor.Grids.ActionEventArgs<ActiveMedication>>(this, ActionBeginHandler))">
            </GridEvents>
            <GridColumns>
                <GridColumn Field="MedicineId" IsPrimaryKey="true" Visible="false"></GridColumn>
                <GridColumn Field="BrandName" HeaderText="@Localizer["Brand Name"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                <GridColumn Field="DrugDetails" HeaderText="@Localizer["Drug Details"]" TextAlign="TextAlign.Center" Width="200"></GridColumn>
                <GridColumn Field="Quantity" HeaderText="@Localizer["Quantity"]" TextAlign="TextAlign.Center" Width="100" DefaultValue="1"></GridColumn>
                <GridColumn Field="Frequency" HeaderText="@Localizer["Frequency"]" TextAlign="TextAlign.Center" Width="120"></GridColumn>
                <GridColumn Field="StartDate" HeaderText="@Localizer["Start Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>
                <GridColumn Field="EndDate" HeaderText="@Localizer["End Date"]" TextAlign="TextAlign.Center" Width="100" Format="MM/dd/y"></GridColumn>

                <GridColumn Field="@nameof(ActiveMedication.CheifComplaint)"
                HeaderText="@Localizer["Chief Complaint"]"
                TextAlign="TextAlign.Center"
                Width="200"
                EditTemplate="@ChiefComplaintEditTemplate">
                </GridColumn>

                <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="100">
                    <GridCommandColumns>
                        <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                    </GridCommandColumns>
                </GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
    <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0;">
        <MudButton Color="Color.Secondary"
        Variant="Variant.Outlined"
        OnClick="@(EventCallback.Factory.Create(this, CancelChanges))"
        Dense="true"
        Style="min-width: 120px; height: 40px; font-weight: 600;">
            @Localizer["Cancel"]
        </MudButton>
        <MudButton Color="Color.Primary"
        Variant="Variant.Filled"
        OnClick="@(EventCallback.Factory.Create(this, SaveChanges))"
        Dense="true"
        Style="min-width: 120px; height: 40px; font-weight: 600;">
            @Localizer["Save"]
        </MudButton>
    </div>
</div>

