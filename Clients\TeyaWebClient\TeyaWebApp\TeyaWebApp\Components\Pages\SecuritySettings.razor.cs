using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Syncfusion.Blazor.Grids;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.TeyaAIScribeResources;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class SecuritySettings : ComponentBase
    {
        [Inject] private ILogger<SecuritySettings> Logger { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IProductOrganizationMappingService ProductOrganizationMappingService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IProductService ProductService { get; set; }
        [Inject] private ActiveUser user { get; set; }

        private List<Organization>? Organizations { get; set; }
        private List<Product>? Products { get; set; }
        private List<Product>? AllProducts { get; set; }
        private List<Product>? AvailableProducts { get; set; }
        private Organization? _selectedOrganization;
        private Guid? SelectedProductToAdd { get; set; }
        private bool IsAdding { get; set; } = false;
        private bool IsRemoving { get; set; } = false;
        private List<ProductOrganizationMapping>? CurrentMappings { get; set; }

        private Organization? _previousSelectedOrganization;
        private SfGrid<Product>? ProductGrid;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                Organizations = await OrganizationService.GetAllOrganizationsAsync();
                AllProducts = await ProductService.GetProductsAsync();

                Logger.LogInformation(Localizer["LoadedOrgAndProductCount"], Organizations?.Count ?? 0, AllProducts?.Count ?? 0);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["OrgProductInitError"]);
                Organizations = new List<Organization>();
                AllProducts = new List<Product>();
            }
        }

        private Organization? SelectedOrganization
        {
            get => _selectedOrganization;
            set
            {
                if (_selectedOrganization != value)
                {
                    _selectedOrganization = value;
                    _ = OnOrganizationChanged();
                }
            }
        }

        private async Task OnOrganizationChanged()
        {
            if (SelectedOrganization != null)
                await LoadProductsForOrganization();
            else
                await ClearProductSelection();
        }

        private async Task<IEnumerable<Organization>> SearchOrganizations(string value, CancellationToken token)
        {
            await Task.Delay(10, token);
            return Organizations == null || !Organizations.Any()
                ? Enumerable.Empty<Organization>()
                : string.IsNullOrEmpty(value)
                ? Organizations
                : Organizations
                    .Where(o => o.OrganizationName.Contains(value, StringComparison.InvariantCultureIgnoreCase));
        }

        private async Task LoadProductsForOrganization()
        {
            if (SelectedOrganization == null) return;

            SelectedProductToAdd = null;

            try
            {
                CurrentMappings = new List<ProductOrganizationMapping>();
                Products = new List<Product>();

                try
                {
                    var allMappings = await ProductOrganizationMappingService.GetAllProductOrganizationMappingsAsync();
                    CurrentMappings = allMappings?.Where(m => m.OrganizationId == SelectedOrganization.OrganizationId && m.IsActive).ToList() ?? new List<ProductOrganizationMapping>();
                }
                catch (HttpRequestException httpEx) when (httpEx.Message.Contains("404"))
                {
                    Logger.LogInformation(Localizer["NoMappingsFound"], SelectedOrganization.OrganizationName);
                }
                catch (Exception mappingEx)
                {
                    Logger.LogError(mappingEx, Localizer["ErrorFetchingMappings"], SelectedOrganization.OrganizationId);
                }

                if (CurrentMappings.Any() && AllProducts != null)
                {
                    var activeProductIds = CurrentMappings.Select(m => m.ProductId).ToHashSet();
                    Products = AllProducts.Where(p => activeProductIds.Contains(p.Id)).ToList();
                }

                UpdateAvailableProducts();
                Logger.LogInformation(Localizer["LoadedProductAccess"], Products?.Count ?? 0, SelectedOrganization.OrganizationName);
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["UnexpectedErrorLoadProducts"], SelectedOrganization?.OrganizationId);
                Products = new List<Product>();
                CurrentMappings = new List<ProductOrganizationMapping>();
                UpdateAvailableProducts();
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task ClearProductSelection()
        {
            SelectedProductToAdd = null;
            Products = new List<Product>();
            CurrentMappings = new List<ProductOrganizationMapping>();
            AvailableProducts = new List<Product>();
            await InvokeAsync(StateHasChanged);
        }

        private void UpdateAvailableProducts()
        {
            try
            {
                if (AllProducts == null || !AllProducts.Any())
                {
                    AvailableProducts = new List<Product>();
                    return;
                }

                Products ??= new List<Product>();

                var assignedProductIds = Products.Select(p => p.Id).ToHashSet();
                AvailableProducts = AllProducts.Where(p => !assignedProductIds.Contains(p.Id)).ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorUpdatingAvailableProducts"]);
                AvailableProducts = new List<Product>();
            }
        }

        private async Task AddProductToOrganization()
        {
            if (SelectedProductToAdd == null || SelectedOrganization == null || AllProducts == null)
            {
                Logger.LogWarning(Localizer["AddProductMissingData"]);
                return;
            }

            IsAdding = true;
            await InvokeAsync(StateHasChanged);

            try
            {
                var productToAdd = AllProducts.FirstOrDefault(p => p.Id == SelectedProductToAdd.Value);
                if (productToAdd == null)
                {
                    Logger.LogWarning(Localizer["ProductNotFound"]);
                    return;
                }

                var newMapping = new ProductOrganizationMapping
                {
                    ProductOrganizationMappingId = Guid.NewGuid(),
                    ProductId = productToAdd.Id,
                    OrganizationId = SelectedOrganization.OrganizationId,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    ModifiedDate = DateTime.UtcNow,
                    CreatedBy = Guid.Parse(user.id),
                    ModifiedBy = Guid.Parse(user.id)
                };

                await ProductOrganizationMappingService.RegisterProductOrganizationMappingsAsync(newMapping);

                Products ??= new List<Product>();
                Products.Add(productToAdd);

                CurrentMappings ??= new List<ProductOrganizationMapping>();
                CurrentMappings.Add(newMapping);

                UpdateAvailableProducts();
                SelectedProductToAdd = null;

                Logger.LogInformation(Localizer["AddedProductToOrg"], productToAdd.Name, SelectedOrganization.OrganizationName);
                await InvokeAsync(StateHasChanged);
                if (ProductGrid != null) await ProductGrid.Refresh();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorAddingProduct"]);
            }
            finally
            {
                IsAdding = false;
                await InvokeAsync(StateHasChanged);
            }
        }

        private async Task RemoveProductFromOrganization(Product product)
        {
            if (product == null || SelectedOrganization == null || Products == null || CurrentMappings == null)
            {
                Logger.LogWarning(Localizer["RemoveProductMissingData"]);
                return;
            }

            IsRemoving = true;
            await InvokeAsync(StateHasChanged);

            try
            {
                var mappingToDeactivate = CurrentMappings.FirstOrDefault(m =>
                    m.ProductId == product.Id &&
                    m.OrganizationId == SelectedOrganization.OrganizationId &&
                    m.IsActive);

                if (mappingToDeactivate != null)
                {
                    var updatedMapping = new ProductOrganizationMapping
                    {
                        ProductOrganizationMappingId = mappingToDeactivate.ProductOrganizationMappingId,
                        ProductId = mappingToDeactivate.ProductId,
                        OrganizationId = mappingToDeactivate.OrganizationId,
                        IsActive = false,
                        CreatedDate = mappingToDeactivate.CreatedDate,
                        ModifiedDate = DateTime.UtcNow,
                        CreatedBy = mappingToDeactivate.CreatedBy,
                        ModifiedBy = Guid.Parse(user.id)
                    };

                    await ProductOrganizationMappingService.UpdateProductOrganizationMappingByIdAsync(
                        mappingToDeactivate.ProductOrganizationMappingId, updatedMapping);

                    Products.Remove(product);
                    CurrentMappings.Remove(mappingToDeactivate);
                    UpdateAvailableProducts();

                    Logger.LogInformation(Localizer["RemovedProductFromOrg"], product.Name, SelectedOrganization.OrganizationName);
                }
                else
                {
                    Logger.LogWarning(Localizer["NoActiveMappingFound"], product.Id, SelectedOrganization.OrganizationId);
                }

                await InvokeAsync(StateHasChanged);
                if (ProductGrid != null) await ProductGrid.Refresh();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorRemovingProduct"]);
            }
            finally
            {
                IsRemoving = false;
                await InvokeAsync(StateHasChanged);
            }
        }
    }
}
