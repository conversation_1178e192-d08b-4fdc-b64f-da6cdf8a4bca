﻿@page "/PreventiveMedicineSetup"
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.RichTextEditor
@using TeyaUIModels.Model
@using TeyaWebApp.Services
@inject HttpClient Http
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.DropDowns
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer


<MudGrid>
    <MudItem xs="12" md="4">
        <!-- Left Panel - Category/Subcategory/Symptom Hierarchy -->
        <div style="display: flex; flex-direction: column; gap: 16px; height: 80vh;">

            <!-- Categories Section -->
            <MudPaper Elevation="1" Class="pa-4 selection-panel">
                <div class="section-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <MudText Typo="Typo.h6" Class="section-title">@Localizer["Categories"]</MudText>
                    <MudIconButton Icon="@Icons.Material.Filled.Add" OnClick="@(() => ShowForm(FormType.Category))"
                                   Color="Color.Primary" Size="Size.Small" />
                </div>
                <div class="scroll-container">
                    @if (categories == null || !categories.Any())
                    {
                        <MudText Class="py-2">@Localizer["NoCategoriesFound"]</MudText>
                    }
                    else
                    {
                        <MudList T="PMCategory">
                            @foreach (var category in categories)
                            {
                                <MudListItem OnClick="@(() => SelectCategory(category.PMCategoryId))"
                                             Class="@($"category-item hierarchy-item {(_selectedCategoryId == category.PMCategoryId ? "selected" : "")}")">
                                    @category.PMCategoryName
                                </MudListItem>
                            }
                        </MudList>
                        @if (categories.Count() > 5)
                        {
                            <MudText Typo="Typo.caption" Class="ml-2 mt-1">@Localizer["ScrollForMoreCategories"]</MudText>
                        }
                    }
                </div>
            </MudPaper>

            <!-- Subcategories Section -->
            @if (_selectedCategoryId != Guid.Empty)
            {
                var selectedCategory = categories.FirstOrDefault(c => c.PMCategoryId == _selectedCategoryId);
                var categoryName = selectedCategory?.PMCategoryName ?? Localizer["UnnamedCategory"];

                <MudPaper Elevation="1" Class="pa-4 selection-panel">
                    <div class="section-header" style="display: flex; justify-content: space-between; align-items: center;">

                        <MudText Typo="Typo.h6" Class="section-title">
                            @Localizer["SubcategoriesOf"] <span style="color: var(--mud-palette-primary);">@selectedCategory?.PMCategoryName</span>
                        </MudText>


                        <MudIconButton Icon="@Icons.Material.Filled.Add" OnClick="@(() => ShowForm(FormType.Subcategory))"
                                       Color="Color.Primary" Size="Size.Small" />
                    </div>
                    <div class="scroll-container">
                        @{
                            var categorySubs = subCategories.Where(s => s.PMCategoryId == _selectedCategoryId).ToList();
                        }
                        @if (!categorySubs.Any())
                        {
                            <MudText Class="py-2">@Localizer["NoSubcategoriesFound"]</MudText>
                        }
                        else
                        {
                            <MudList T="PMSubCategory">
                                @foreach (var sub in categorySubs)
                                {
                                    <MudListItem OnClick="@(() => SelectSubCategory(sub.PMSubcategoryId))"
                                                 Class="@($"subcategory-item hierarchy-item {(_selectedSubCategoryId == sub.PMSubcategoryId ? "selected" : "")}")">
                                        @sub.PMSubcategoryName
                                    </MudListItem>
                                }
                            </MudList>
                            @if (categorySubs.Count > 5)
                            {
                                <MudText Typo="Typo.caption" Class="ml-2 mt-1">@Localizer["ScrollForMoreSubcategories"]</MudText>
                            }
                        }
                    </div>
                </MudPaper>
            }

            <!-- Symptoms Section -->
            @if (_selectedSubCategoryId != Guid.Empty)
            {
                var selectedSubCategory = subCategories.FirstOrDefault(s => s.PMSubcategoryId == _selectedSubCategoryId);
                var subCategoryName = selectedSubCategory?.PMSubcategoryName ?? Localizer["UnnamedSubcategory"];

                <MudPaper Elevation="1" Class="pa-4 selection-panel">
                    <div class="section-header" style="display: flex; justify-content: space-between; align-items: center;">
                        <MudText Typo="Typo.h6" Class="section-title">
                            @Localizer["SymptomsOf"] <span style="color: var(--mud-palette-primary);">@selectedSubCategory?.PMSubcategoryName</span>
                        </MudText>

                        <MudIconButton Icon="@Icons.Material.Filled.Add" OnClick="@(() => ShowForm(FormType.Symptom))"
                                       Color="Color.Primary" Size="Size.Small" />
                    </div>
                    <div class="scroll-container">
                        @{
                            var subSymptoms = symptoms.Where(s => s.PMSubCategoryId == _selectedSubCategoryId).ToList();
                        }
                        @if (!subSymptoms.Any())
                        {
                            <MudText Class="py-2">@Localizer["NoSymptomsFound"]</MudText>
                        }
                        else
                        {
                            <MudList T="Symptoms">
                                @foreach (var symptom in subSymptoms)
                                {
                                    <MudListItem Class="symptom-item hierarchy-item">
                                        @symptom.PMSymptomName
                                    </MudListItem>
                                }
                            </MudList>
                            @if (subSymptoms.Count > 5)
                            {
                                <MudText Typo="Typo.caption" Class="ml-2 mt-1">@Localizer["ScrollForMoreSymptoms"]</MudText>
                            }
                        }
                    </div>
                </MudPaper>
            }
        </div>
    </MudItem>

    <MudItem xs="12" md="8">
        <!-- Right Panel - Dynamic Forms -->
        @if (activeForm == FormType.Category)
        {
            <MudPaper Elevation="1" Class="pa-4">
                <EditForm Model="_newCategory" OnValidSubmit="AddCategory">
                    <DataAnnotationsValidator />
                    <MudText Typo="Typo.h6" Class="mb-4">@Localizer["AddNewCategory"]</MudText>
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <MudTextField @bind-Value="_newCategory.PMCategoryName"
                                      Label="@Localizer["CategoryName"]"
                                      Required="true"
                                      RequiredError="@Localizer["CategoryNameIsRequired"]"
                                      Variant="Variant.Outlined" />

                        <MudTextField @bind-Value="_newCategory.PMCategoryDescription"
                                      Label="@Localizer["Description"]"
                                      Variant="Variant.Outlined"
                                      Lines="3" />
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 8px; margin-top: 16px;">
                        <MudButton Style="width: 100px; height: 40px;margin-right:10px;" Variant="Variant.Outlined" Color="Color.Secondary" OnClick="CancelForm">
                            @Localizer["Cancel"]
                        </MudButton>
                        <MudButton Style="width: 100px; height: 40px;" Variant="Variant.Filled" Color="Color.Primary" Type="Submit" OnClick="AddCategory">
                            @Localizer["Save"]
                        </MudButton>
                    </div>
                </EditForm>
            </MudPaper>
        }
        else if (activeForm == FormType.Subcategory)
        {
            <MudPaper Elevation="1" Class="pa-4">
                <EditForm Model="_newSubCategory" OnValidSubmit="AddSubCategory">
                    <DataAnnotationsValidator />
                    <MudText Typo="Typo.h6" Class="mb-4">@Localizer["AddNewSubcategory"]</MudText>
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <MudTextField Label="@Localizer["Category"]"
                                      Variant="Variant.Outlined"
                                      Value="@categories.FirstOrDefault(c => c.PMCategoryId == _selectedCategoryId)?.PMCategoryName"
                                      Disabled="true" />

                        <MudTextField @bind-Value="_newSubCategory.PMSubcategoryName"
                                      Label="@Localizer["SubcategoryName"]"
                                      Required="true"
                                      RequiredError="@Localizer["SubcategoryNameIsRequired"]"
                                      Variant="Variant.Outlined" />

                        <MudTextField @bind-Value="_newSubCategory.PMSubcategoryDescription"
                                      Label="@Localizer["Description"]"
                                      Variant="Variant.Outlined"
                                      Lines="3" />
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 8px; margin-top: 16px;">
                        <MudButton Style="width: 100px; height: 40px;margin-right:10px;" Variant="Variant.Outlined" Color="Color.Secondary" OnClick="CancelForm">
                            @Localizer["Cancel"]
                        </MudButton>
                        <MudButton Style="width: 100px; height: 40px;" Variant="Variant.Filled" Color="Color.Primary" Type="Submit" OnClick="AddSubCategory">
                            @Localizer["Save"]
                        </MudButton>
                    </div>
                </EditForm>
            </MudPaper>
        }
        else if (activeForm == FormType.Symptom)
        {
            <MudPaper Elevation="1" Class="pa-4">
                <EditForm Model="_newSymptom" OnValidSubmit="AddSymptom">
                    <DataAnnotationsValidator />
                    <MudText Typo="Typo.h6" Class="mb-4">@Localizer["AddNewSymptom"]</MudText>
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; gap: 20px;">
                            <MudTextField Label="@Localizer["Category"]"
                                          Variant="Variant.Outlined"
                                          Value="@categories.FirstOrDefault(c => c.PMCategoryId == _selectedCategoryIdForSymptom)?.PMCategoryName"
                                          Style="flex: 1;"
                                          Disabled="true" />

                            @if (_selectedSubCategoryIdForSymptom == Guid.Empty)
                            {
                                <MudSelect T="Guid" @bind-Value="_selectedSubCategoryIdForSymptom"
                                           Label="@Localizer["SelectSubcategory"]"
                                           Variant="Variant.Outlined"
                                           Style="flex: 1;"
                                           Required="true"
                                           RequiredError="@Localizer["PleaseSelectASubcategory"]">
                                    @if (_filteredSubCategoriesForSymptom.Count == 0)
                                    {
                                        <MudSelectItem Value="@Guid.Empty">@Localizer["NoSubcategoriesAvailable"]</MudSelectItem>
                                    }
                                    else
                                    {
                                        @foreach (var sub in _filteredSubCategoriesForSymptom)
                                        {
                                            <MudSelectItem Value="@sub.PMSubcategoryId">@sub.PMSubcategoryName</MudSelectItem>
                                        }
                                    }
                                </MudSelect>
                            }
                            else
                            {
                                <MudTextField Label="@Localizer["Subcategory"]"
                                              Variant="Variant.Outlined"
                                              Value="@subCategories.FirstOrDefault(s => s.PMSubcategoryId == _selectedSubCategoryIdForSymptom)?.PMSubcategoryName"
                                              Style="flex: 1;"
                                              Disabled="true" />
                            }
                        </div>

                        <MudTextField @bind-Value="_newSymptom.PMSymptomName"
                                      Label="@Localizer["SymptomName"]"
                                      Required="true"
                                      RequiredError="@Localizer["SymptomNameIsRequired"]"
                                      Variant="Variant.Outlined" />

                        <MudTextField @bind-Value="_newSymptom.PMSymptomDescription"
                                      Label="@Localizer["Description"]"
                                      Variant="Variant.Outlined"
                                      Lines="3" />
                    </div>
                    <div style="display: flex; justify-content: flex-end; gap: 8px; margin-top: 16px;">
                        <MudButton Style="width: 100px; height: 40px;margin-right:10px;" Variant="Variant.Outlined" Color="Color.Secondary" OnClick="CancelForm">
                            @Localizer["Cancel"]
                        </MudButton>
                        <MudButton Style="width: 100px; height: 40px;" Variant="Variant.Filled" Color="Color.Primary" Type="Submit" OnClick="AddSymptom">
                            @Localizer["Save"]
                        </MudButton>

                    </div>
                </EditForm>
            </MudPaper>
        }
        else
        {
            <MudPaper Elevation="1" Class="pa-4" Style="height: 80vh; display: flex; align-items: center; justify-content: center;">
                <MudText Typo="Typo.body1">@Localizer["AddItem"]</MudText>
            </MudPaper>
        }
    </MudItem>
</MudGrid>


<style>
    /* Updated Visual Hierarchy with Font Weights */
    .category-item {
        font-weight: 900; /* Boldest for categories */
        padding: 8px 12px;
        border-radius: 4px;
    }

    .subcategory-item {
        font-weight: 700; /* Slightly less bold for subcategories */
        padding: 8px 12px 8px 24px; /* Indent subcategories */
        border-radius: 4px;
    }

    .symptom-item {
        font-weight: 550; /* Regular weight for symptoms */
        padding: 8px 12px 8px 36px; /* More indent for symptoms */
        border-radius: 4px;
    }

    /* Selected states with subtle backgrounds */
    .category-item.selected {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.09);
        border-left: 5px solid var(--mud-palette-primary);
    }

    .subcategory-item.selected {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.07);
        border-left: 3px solid var(--mud-palette-primary);
    }

    /* Hover states */
    .hierarchy-item:hover {
        background-color: var(--mud-palette-action-hover) !important;
    }

    /* Section headers */
    .section-header {
        border-bottom: 1px solid var(--mud-palette-lines-default);
        padding-bottom: 8px;
        margin-bottom: 12px;
    }

    .section-title {
        font-weight: 600;
        color: var(--mud-palette-text-primary);
    }

    /* Scroll container */
    .scroll-container {
        max-height: 200px;
        overflow-y: auto;
        padding-right: 4px;

    }
</style>
