using Microsoft.AspNetCore.Components;
using Microsoft.CognitiveServices.Speech.Transcription;
using Microsoft.Extensions.Localization;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.Data;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.Inputs;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static TeyaUIModels.Model.DentalClaims;

namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class DentalClaim : ComponentBase
    {
        private CompleteDentalClaims CompleteDentalClaims = new();
        private DentalClaims DentalClaims = new();
        private Member Patient = new();

        [Inject]
        private ActiveUser User { get; set; }

        [Inject]
        private IOrganizationService OrganizationService { get; set; }

        [Inject]
        private ISnackbar Snackbar { get; set; }

        private List<Insurance> InsuranceData = new();
        private bool isLoading = true;
        private bool isSubmitting = false;
        private List<DentalClaimsCharges> ServiceCharges = new List<DentalClaimsCharges>();
        private DateTime? ServiceDate { get; set; } = DateTime.Today;
        private string selectedDatabase = Source.CMS.ToString();
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        private List<FDB_ICD> fdb_ICD { get; set; } = new List<FDB_ICD>();
        private bool Subscription = false;
        private Guid OrganizationID { get; set; }


        private List<Member> PatientList = new List<Member>();
        private Member selectedPatient { get; set; }

        private List<DentalClaimsCharges> serviceCharges = new List<DentalClaimsCharges>();
        private bool showAddForm = false;
        private DentalClaimsCharges newCharge = new();
        private DateTime? newChargeDOS = DateTime.Today;
        private SfGrid<DentalClaimsCharges> ServiceGrid;


        // Additional properties for dental claim form
        private string? ClaimNumber { get; set; }
        private bool DentistPretreatmentEstimates { get; set; }
        private bool DentistStatementOfActualServices { get; set; }
        private bool MedicaidClaim { get; set; }
        private bool EPSDT { get; set; }
        private string? PriorAuthorizationNumber { get; set; }
        private bool IsOccupationalInjury { get; set; }
        private bool IsAutoAccident { get; set; }
        private bool IsOtherAccident { get; set; }
        private bool IsProsthesis { get; set; }
        private string ReplacementReason { get; set; } = "";
        private DateTime? PriorDate { get; set; }
        public bool OrthodonticsTreatment { get; set; }
        private string PlaceOfTreatment { get; set; } = "Office";
        private bool HasRadiographs { get; set; }
        private bool BillToPatient { get; set; }
        private decimal Copay { get; set; }

        private int currentClaimNumber = 0;
        private int lastUsedClaimNumber = 0;

        [Inject] private ILogger<SurgicalHistory> _logger { get; set; }
        // Remarks
        private string? Remarks { get; set; }
        [Inject] public IICDService _ICDService { get; set; }
        private List<DentalClaimsICD> AddedICDCodes = new List<DentalClaimsICD>();
        private SfGrid<DentalClaimsICD> ICDGrid;
        private string currentICDSelection = "";
        public enum ClaimStatus
        {
            Pending,
            Completed
        }

        private List<string> statusOptions = Enum.GetNames(typeof(ClaimStatus)).ToList();

        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrganizationID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrganizationID);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == Localizer["Enterprise"];
                _icdCodes = await _ICDService.GetAllICDCodesAsync();

                // Initialize CompleteDentalClaims
                InitializeCompleteDentalClaims();

                // Generate claim number immediately
                await GenerateClaimNumber();

                // Load initial data
                await LoadPatients();
                await LoadClaimData();
                await LoadInsuranceData();

                // Initialize with one service charge
                AddServiceCharge();

                isLoading = false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during initialization: {ex.Message}");
                Snackbar?.Add("Error loading page data", Severity.Error);
                isLoading = false;
            }
        }

        private void InitializeCompleteDentalClaims()
        {
            CompleteDentalClaims = new CompleteDentalClaims
            {
                Id = Guid.NewGuid(),
                dentalclaims = new DentalClaims(),
                dentalClaimsCharges = new List<DentalClaimsCharges>(),
                dentalClaimsICD = new List<DentalClaimsICD>(),
                OrganizationID = OrganizationID,
                Subscription = Subscription
            };

            DentalClaims = CompleteDentalClaims.dentalclaims;
            ServiceCharges = CompleteDentalClaims.dentalClaimsCharges;
            AddedICDCodes = CompleteDentalClaims.dentalClaimsICD;
        }

        private async Task LoadPatients()
        {
            try
            {
                PatientList = await MemberService.GetAllMembersAsync(OrganizationID, Subscription);
                PatientList = PatientList.Where(p => p.RoleName == "Patient").ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading patients: {ex.Message}");
                Snackbar?.Add("Error loading patients", Severity.Error);
            }
        }

        private async Task OnPatientChanged(Member selectedPatients)
        {
            selectedPatient = selectedPatients;
            Patient = selectedPatients;

            // Auto-fill the DentalClaims.PatientInfo field with patient details
            if (selectedPatients != null)
            {
                DentalClaims.PatientInfo = FormatPatientInfo(selectedPatients);
            }
            else
            {
                DentalClaims.PatientInfo = "";
            }

            // Load insurance data for selected patient
            if (selectedPatients?.InsuranceId != null)
            {
                await LoadInsuranceDataForPatient(selectedPatients);
            }

            StateHasChanged();
        }

        // Updated FormatPatientInfo method to include all required fields
        private string FormatPatientInfo(Member patient)
        {
            if (patient == null) return "No Patient Selected";
            var patientDetails = new List<string>();

            // Add patient name
            if (!string.IsNullOrEmpty(patient.UserName))
            {
                patientDetails.Add(patient.UserName);
            }

            // Format and add DOB
            if (patient.DateOfBirth != null)
            {
                if (DateTime.TryParse(patient.DateOfBirth.ToString(), out DateTime dob))
                {
                    patientDetails.Add(dob.ToString("MM/dd/yyyy"));
                }
                else
                {
                    patientDetails.Add(patient.DateOfBirth.ToString());
                }
            }

            // Add Sexual Orientation/Gender
            if (!string.IsNullOrEmpty(patient.SexualOrientation))
            {
                patientDetails.Add(patient.SexualOrientation);
            }


            // Add Country
            if (!string.IsNullOrEmpty(patient.Country))
            {
                patientDetails.Add(patient.Country);
            }

            // Join all details with new lines
            return patientDetails.Any() ? string.Join(Environment.NewLine, patientDetails) : "Patient Information Not Available";
        }

        private async Task RemoveSelectedPatient()
        {
            try
            {
                selectedPatient = null;
                Patient = new Member();
                DentalClaims.PatientInfo = "";
                InsuranceData.Clear();

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error removing patient: {ex.Message}");
            }
        }


        private async Task LoadInsuranceDataForPatient(Member patient)
        {
            try
            {
                if (patient.InsuranceId.HasValue)
                {
                    var insurance = await InsuranceService.GetInsuranceByIdAsync(
                        patient.InsuranceId.Value,
                        OrganizationID,
                        Subscription);

                    if (insurance != null)
                    {
                        InsuranceData = new List<Insurance> { insurance };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading insurance data: {ex.Message}");
                Snackbar?.Add("Error loading insurance data", Severity.Warning);
            }
        }

        private async Task OnICDNameChanged(string value)
        {
            currentICDSelection = value;
            StateHasChanged();
        }

        protected Task<IEnumerable<string>> SearchICDCodes(string value, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            IEnumerable<string> searchResults = Enumerable.Empty<string>();

            if (selectedDatabase == Source.CMS.ToString())
            {
                searchResults = _icdCodes
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (
                            (!string.IsNullOrEmpty(icd.Code) && icd.Code.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.Description) && icd.Description.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                searchResults = fdb_ICD
                    .Where(icd =>
                        (!string.IsNullOrWhiteSpace(value)) &&
                        (icd.ICD_CD_TYPE == "06" || icd.ICD_CD_TYPE == "05") &&
                        (
                            (!string.IsNullOrEmpty(icd.ICD_CD) && icd.ICD_CD.Contains(value, StringComparison.OrdinalIgnoreCase)) ||
                            (!string.IsNullOrEmpty(icd.ICD_DESC) && icd.ICD_DESC.Contains(value, StringComparison.OrdinalIgnoreCase))
                        ))
                    .Select(icd => $"{icd.ICD_CD} - {icd.ICD_DESC ?? ""}")
                    .ToList();
            }

            return Task.FromResult(searchResults);
        }

        private async Task AddICDCode()
        {
            if (!string.IsNullOrEmpty(currentICDSelection))
            {
                // Parse the selected ICD code (assuming format like "A00.0 - Cholera due to Vibrio cholerae")
                var parts = currentICDSelection.Split(" - ", 2);
                if (parts.Length >= 2)
                {
                    var code = parts[0].Trim();
                    var description = parts[1].Trim();

                    // Check if this ICD code is already added
                    if (AddedICDCodes.Any(x => x.Code == code))
                    {
                        // Show message that code is already added
                        // You can use your notification system here
                        return;
                    }

                    // Create new ICD entry
                    var newICD = new DentalClaimsICD
                    {
                        ICDId = Guid.NewGuid(), // or however you generate IDs
                        Code = code,
                        Description = description
                    };

                    // Add to the list
                    AddedICDCodes.Add(newICD);

                    // Clear the selection
                    currentICDSelection = string.Empty;

                    // Refresh the grid
                    if (ICDGrid != null)
                    {
                        await ICDGrid.Refresh();
                    }

                    StateHasChanged();
                }
            }
        }

        private async Task LoadClaimData()
        {
            try
            {
                if (CompleteDentalClaims.Id != Guid.Empty)
                {
                    var existingClaims = await DentalClaimsService.GetDentalClaimsByIdAsync(
                        CompleteDentalClaims.Id,
                        OrganizationID,
                        Subscription);

                    if (existingClaims != null && existingClaims.Any())
                    {
                        var existingClaim = existingClaims.First();
                        CompleteDentalClaims = existingClaim;
                        DentalClaims = CompleteDentalClaims.dentalclaims ?? new DentalClaims();
                        ServiceCharges = CompleteDentalClaims.dentalClaimsCharges ?? new List<DentalClaimsCharges>();
                        StateHasChanged();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading claim data: {ex.Message}");
            }
        }

        private void AddServiceCharge()
        {
            serviceCharges.Add(new DentalClaimsCharges
            {
                ChargesId = Guid.NewGuid(),
                Description = "",
                DOS = "",
                ToothNumber = "",
                Surface = "",
                Fee = 0.00m,
            });

            UpdateTotalCharges();
            StateHasChanged();
        }

        private async Task LoadInsuranceData()
        {
            try
            {
                if (Patient?.InsuranceId != null)
                {
                    var insurance = await InsuranceService.GetInsuranceByIdAsync(
                        Patient.InsuranceId.Value,
                        OrganizationID,
                        Subscription);

                    if (insurance != null)
                    {
                        InsuranceData = new List<Insurance> { insurance };
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading insurance data: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            try
            {
                if (!ServiceCharges.Any())
                {
                    AddServiceCharge();
                }

                if (OrganizationID == Guid.Empty)
                {
                    Snackbar?.Add("Organization information is missing. Please reload the page.", Severity.Error);
                    return false;
                }

                foreach (var charge in ServiceCharges)
                {
                    if (charge.Fee <= 0)
                    {
                        Snackbar?.Add("All service charges must have a fee greater than 0.", Severity.Warning);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Validation error: {ex.Message}");
                return false;
            }
        }

        private async Task GenerateClaimNumber()
        {
            if (string.IsNullOrEmpty(ClaimNumber))
            {
                try
                {
                    // Get the next claim number from the database
                    int nextClaimNumber = await GetNextClaimNumber();
                    ClaimNumber = nextClaimNumber.ToString();

                    Console.WriteLine($"Generated claim number: {ClaimNumber}");

                    // Update the DentalClaims object immediately
                    DentalClaims.ClaimNumber = ClaimNumber;
                    StateHasChanged(); // Trigger UI update
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error generating claim number: {ex.Message}");
                    // Fallback to session-based incrementing
                    int fallbackNumber = Math.Max(1, lastUsedClaimNumber + 1);
                    ClaimNumber = fallbackNumber.ToString();
                    Console.WriteLine($"Using fallback claim number: {ClaimNumber}");
                    DentalClaims.ClaimNumber = ClaimNumber;
                    StateHasChanged();
                }
            }
        }

        // New method to get the next sequential claim number
        // Updated method to get the next sequential claim number per organization
        private async Task<int> GetNextClaimNumber()
        {
            try
            {
                // Get existing claims for the current organization (service already filters by OrganizationID)
                var existingClaims = await DentalClaimsService.GetAllDentalClaimsAsync(OrganizationID, Subscription);

                if (existingClaims != null && existingClaims.Any())
                {
                    int maxClaimNumber = 0;

                    // Find max claim number for this organization
                    foreach (var claim in existingClaims)
                    {
                        if (claim.dentalclaims != null &&
                            !string.IsNullOrEmpty(claim.dentalclaims.ClaimNumber) &&
                            int.TryParse(claim.dentalclaims.ClaimNumber, out int claimNum))
                        {
                            if (claimNum > maxClaimNumber)
                            {
                                maxClaimNumber = claimNum;
                            }
                        }
                    }

                    // Increment from this organization's maximum claim number
                    int nextNumber = maxClaimNumber + 1;
                    Console.WriteLine($"Organization {OrganizationID} - Database max: {maxClaimNumber}, Next: {nextNumber}");

                    // Update session tracking for this organization
                    lastUsedClaimNumber = maxClaimNumber;

                    return nextNumber;
                }
                else
                {
                    // If no existing claims for this organization, start from 1
                    Console.WriteLine($"No existing claims for organization {OrganizationID}, starting from: 1");
                    lastUsedClaimNumber = 0;
                    return 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting next claim number for organization {OrganizationID}: {ex.Message}");

                try
                {
                    // Fallback: try to get max claim number for current organization
                    var existingClaims = await DentalClaimsService.GetAllDentalClaimsAsync(OrganizationID, Subscription);
                    var maxFromDb = existingClaims?
                        .Where(c => c.dentalclaims != null &&
                                   !string.IsNullOrEmpty(c.dentalclaims.ClaimNumber))
                        .Select(c => int.TryParse(c.dentalclaims.ClaimNumber, out int num) ? num : 0)
                        .DefaultIfEmpty(0)
                        .Max() ?? 0;

                    Console.WriteLine($"Fallback for organization {OrganizationID}: max = {maxFromDb}, returning {maxFromDb + 1}");
                    return maxFromDb + 1;
                }
                catch
                {
                    // Ultimate fallback - start from 1 for new organization
                    Console.WriteLine($"Ultimate fallback for organization {OrganizationID}: starting from 1");
                    return 1;
                }
            }
        }

        private void PopulateClaimData()
        {
            try
            {
                if (string.IsNullOrEmpty(ClaimNumber))
                {
                    GenerateClaimNumber(); // or use the property getter
                }

                DentalClaims.Id = CompleteDentalClaims.Id;
                DentalClaims.ClaimNumber = ClaimNumber;
                DentalClaims.ServiceDate = DentalClaims.ServiceDate ?? DateTime.Today;
                DentalClaims.ClaimDate = DentalClaims.ClaimDate ?? DateTime.Today;
                DentalClaims.OrganizationID = OrganizationID;
                DentalClaims.Subscription = Subscription;
                DentalClaims.Status = DentalClaims.Status ?? "Pending";

                DentalClaims.PlaceOfTreatment = DentalClaims.PlaceOfTreatment ?? "Office";
                DentalClaims.DentistPretreatmentEstimates = DentalClaims.DentistPretreatmentEstimates;
                DentalClaims.DentistStatementOfActualServices = DentalClaims.DentistStatementOfActualServices;
                DentalClaims.MedicaidClaim = DentalClaims.MedicaidClaim;
                DentalClaims.EPSDT = DentalClaims.EPSDT;
                DentalClaims.PriorAuthorizationNumber = DentalClaims.PriorAuthorizationNumber ?? "";
                DentalClaims.IsOccupationalInjury = DentalClaims.IsOccupationalInjury;
                DentalClaims.IsAutoAccident = DentalClaims.IsAutoAccident;
                DentalClaims.IsOtherAccident = DentalClaims.IsOtherAccident;
                DentalClaims.IsProsthesis = DentalClaims.IsProsthesis;
                DentalClaims.ReplacementReason = DentalClaims.ReplacementReason ?? "";
                DentalClaims.PriorDate = DentalClaims.PriorDate;
                DentalClaims.OrthodonticsTreatment = DentalClaims.OrthodonticsTreatment;
                DentalClaims.HasRadiographs = DentalClaims.HasRadiographs;
                DentalClaims.BillToPatient = DentalClaims.BillToPatient;
                DentalClaims.Copay = DentalClaims.Copay;
                CompleteDentalClaims.dentalClaimsICD = AddedICDCodes.ToList();

                // Ensure PatientInfo is properly formatted (this will already be set from OnPatientChanged)
                if (selectedPatient != null && string.IsNullOrEmpty(DentalClaims.PatientInfo))
                {
                    DentalClaims.PatientInfo = FormatPatientInfo(selectedPatient);
                }

                DentalClaims.FacilityInfo = $"{User?.OrganizationName ?? "Unknown Organization"}";

                DentalClaims.TotalCharges = ServiceCharges.Sum(c => c.Fee);

                if (DentalClaims.TotalPayments == 0 && DentalClaims.TotalCharges > 0)
                {
                    DentalClaims.TotalBalance = DentalClaims.TotalCharges;
                }

                foreach (var charge in ServiceCharges)
                {
                    charge.DentalClaimsId = CompleteDentalClaims.Id;
                    if (charge.ChargesId == Guid.Empty)
                    {
                        charge.ChargesId = Guid.NewGuid();
                    }
                }

                foreach (var icdCode in AddedICDCodes)
                {
                    icdCode.DentalClaimsId = CompleteDentalClaims.Id;
                    if (icdCode.ICDId == Guid.Empty)
                    {
                        icdCode.ICDId = Guid.NewGuid();
                    }
                }

                CompleteDentalClaims.dentalclaims = DentalClaims;
                CompleteDentalClaims.dentalClaimsCharges = ServiceCharges;
                CompleteDentalClaims.dentalClaimsICD = AddedICDCodes;
                CompleteDentalClaims.OrganizationID = OrganizationID;
                CompleteDentalClaims.Subscription = Subscription;

                if (!string.IsNullOrEmpty(Remarks))
                {
                    try
                    {
                        var remarksProperty = typeof(DentalClaims).GetProperty("Remarks");
                        remarksProperty?.SetValue(DentalClaims, Remarks);
                    }
                    catch
                    {
                        // Ignore if property doesn't exist
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error populating claim data: {ex.Message}");
                throw;
            }
        }

        private async Task OnSubmit()
        {
            if (isSubmitting) return;

            try
            {
                isSubmitting = true;

                Console.WriteLine("Starting form submission...");
                if (!ValidateForm())
                {
                    Console.WriteLine("Form validation failed");
                    return;
                }

                Console.WriteLine("Form validation passed");
                PopulateClaimData();

                Console.WriteLine($"Populated claim data with {ServiceCharges.Count} service charges");
                Console.WriteLine($"Submitting CompleteDentalClaims: ID={CompleteDentalClaims.Id}, PatientInfo={DentalClaims.PatientInfo}, TotalCharges={DentalClaims.TotalCharges}");
                Console.WriteLine($"Current claim number being submitted: {ClaimNumber}");

                foreach (var charge in ServiceCharges)
                {
                    Console.WriteLine($"Charge: ID={charge.ChargesId}, DentalClaimsId={charge.DentalClaimsId}, Fee={charge.Fee}, Description={charge.Description}");
                }

                await DentalClaimsService.AddDentalClaimsAsync(new List<CompleteDentalClaims> { CompleteDentalClaims }, OrganizationID, Subscription);

                Console.WriteLine("Successfully submitted to database");

                // Update the last used claim number
                if (int.TryParse(ClaimNumber, out int submittedClaimNumber))
                {
                    lastUsedClaimNumber = submittedClaimNumber;
                    Console.WriteLine($"Updated lastUsedClaimNumber to: {lastUsedClaimNumber}");
                }

                Snackbar?.Add("Dental claim submitted successfully!", Severity.Success);

                // After successful submission, prepare for next claim
                await ResetFormForNextClaim();

                // Generate the next claim number immediately and update the UI
                ClaimNumber = null; // Clear current claim number
                await GenerateClaimNumber(); // Generate next sequential number

                Console.WriteLine($"Next claim number generated: {ClaimNumber}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error submitting dental claim: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                var errorMessage = $"Error submitting claim: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $" Inner exception: {ex.InnerException.Message}";
                }

                Snackbar?.Add(errorMessage, Severity.Error);
            }
            finally
            {
                isSubmitting = false;
                StateHasChanged();
            }
        }

        // New method specifically for resetting after successful submission
        private async Task ResetFormForNextClaim()
        {
            try
            {
                selectedPatient = null;
                Patient = new Member();
                ServiceCharges.Clear();
                AddedICDCodes.Clear();
                currentICDSelection = "";
                ServiceDate = DateTime.Today;
                Remarks = "";
                InsuranceData.Clear();

                DentistPretreatmentEstimates = false;
                DentistStatementOfActualServices = false;
                MedicaidClaim = false;
                EPSDT = false;
                PriorAuthorizationNumber = null;
                IsOccupationalInjury = false;
                IsAutoAccident = false;
                IsOtherAccident = false;
                IsProsthesis = false;
                ReplacementReason = "";
                PriorDate = null;
                OrthodonticsTreatment = false;
                PlaceOfTreatment = "Office";
                DentalClaims.Status = "Pending";
                HasRadiographs = false;
                BillToPatient = false;
                Copay = 0;

                InitializeCompleteDentalClaims();

                // DON'T generate claim number here - it will be done in OnSubmit
                // await GenerateClaimNumber();

                AddServiceCharge();
                DentalClaims.TotalCharges = 0;
                DentalClaims.TotalBalance = 0;
                DentalClaims.PatientUncoveredAmount = 0;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error resetting form for next claim: {ex.Message}");
            }
        }

        // Update the existing ResetForm method to also use await for GenerateClaimNumber
        private async Task ResetForm()
        {
            try
            {
                selectedPatient = null;
                Patient = new Member();
                ServiceCharges.Clear();
                AddedICDCodes.Clear();
                currentICDSelection = "";
                ServiceDate = DateTime.Today;
                Remarks = "";
                InsuranceData.Clear();

                // Clear claim number so new one gets generated
                ClaimNumber = null;

                DentistPretreatmentEstimates = false;
                DentistStatementOfActualServices = false;
                MedicaidClaim = false;
                EPSDT = false;
                PriorAuthorizationNumber = null;
                IsOccupationalInjury = false;
                IsAutoAccident = false;
                IsOtherAccident = false;
                IsProsthesis = false;
                ReplacementReason = "";
                PriorDate = null;
                OrthodonticsTreatment = false;
                PlaceOfTreatment = "Office";
                DentalClaims.Status = "Pending";
                HasRadiographs = false;
                BillToPatient = false;
                Copay = 0;

                InitializeCompleteDentalClaims();

                // Generate new claim number for the reset form
                await GenerateClaimNumber();

                AddServiceCharge();
                DentalClaims.TotalCharges = 0;
                DentalClaims.TotalBalance = 0;
                DentalClaims.PatientUncoveredAmount = 0;

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error resetting form: {ex.Message}");
            }
        }

        private void OnCancel()
        {
            _ = ResetForm();
            Console.WriteLine("Form cancelled");
        }


        private async Task ActionBeginHandler(ActionEventArgs<DentalClaimsCharges> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    if (!ValidateServiceCharge(args.Data))
                    {
                        args.Cancel = true;
                        return;
                    }

                    if (args.Data.ChargesId == Guid.Empty)
                    {
                        args.Data.ChargesId = Guid.NewGuid();
                    }
                    args.Data.DentalClaimsId = CompleteDentalClaims.Id;

                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting service charge: {args.Data?.Description}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new DentalClaimsCharges
                    {
                        ChargesId = Guid.NewGuid(),
                        DentalClaimsId = CompleteDentalClaims.Id,
                        DOS = "",
                        Fee = 0.00m,
                        ToothNumber = "",
                        Surface = "",
                        Description = ""
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing grid action", Severity.Error);
            }
        }

        private void CalculatePatientUncoveredAmount()
        {
            DentalClaims.PatientUncoveredAmount = DentalClaims.TotalBalance - DentalClaims.Copay;
            StateHasChanged();
        }
        private void UpdateTotalCharges()
        {
            DentalClaims.TotalCharges = ServiceCharges.Sum(c => c.Fee);
            DentalClaims.TotalBalance = DentalClaims.TotalCharges - DentalClaims.TotalPayments;
            StateHasChanged();
        }

        private void OnCopayChanged(decimal value)
        {
            DentalClaims.Copay = value;
            CalculatePatientUncoveredAmount();
            StateHasChanged();
        }

        // Add method to handle OK button click
        private void OnOkClicked()
        {
            DentalClaims.PatientCharges = DentalClaims.Copay;

            // Calculate Patient Balance = Patient Charges - Patient Payments
            DentalClaims.PatientBalance = DentalClaims.PatientCharges - DentalClaims.PatientPayments;
            // Ensure the calculation is up to date
            CalculatePatientUncoveredAmount();

        }

        private void OnTotalPaymentsChanged(decimal value)
        {
            DentalClaims.TotalPayments = value;
            DentalClaims.TotalBalance = DentalClaims.TotalCharges - DentalClaims.TotalPayments;
            CalculatePatientUncoveredAmount();
            StateHasChanged();
        }
        private async Task ActionCompletedHandler(ActionEventArgs<DentalClaimsCharges> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    CompleteDentalClaims.dentalClaimsCharges = ServiceCharges.ToList();
                    UpdateTotalCharges(); // Add this line
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    CompleteDentalClaims.dentalClaimsCharges = ServiceCharges.ToList();
                    UpdateTotalCharges(); // Add this line
                    Snackbar?.Add("Service charge deleted successfully!", Severity.Info);
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    CompleteDentalClaims.dentalClaimsCharges = ServiceCharges.ToList();
                    UpdateTotalCharges(); // Add this line
                }

                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ActionCompletedHandler: {ex.Message}");
                Snackbar?.Add("Error completing grid action", Severity.Error);
            }
        }

        private async Task ICDActionBeginHandler(ActionEventArgs<DentalClaimsICD> args)
        {
            try
            {
                if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
                {
                    // Validate ICD data if needed
                    if (string.IsNullOrEmpty(args.Data.Code))
                    {
                        args.Cancel = true;
                        Snackbar?.Add("ICD Code is required", Severity.Error);
                        return;
                    }

                    if (args.Data.ICDId == Guid.Empty)
                    {
                        args.Data.ICDId = Guid.NewGuid();
                    }
                    args.Data.DentalClaimsId = CompleteDentalClaims.Id;
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
                {
                    Console.WriteLine($"Deleting ICD code: {args.Data?.Code}");
                }
                else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
                {
                    args.Data = new DentalClaimsICD
                    {
                        ICDId = Guid.NewGuid(),
                        DentalClaimsId = CompleteDentalClaims.Id,
                        Code = "",
                        Description = ""
                    };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ICDActionBeginHandler: {ex.Message}");
                args.Cancel = true;
                Snackbar?.Add("Error processing ICD grid action", Severity.Error);
            }
        }

        // Keep your existing action completed handler
        private async Task ICDActionCompletedHandler(ActionEventArgs<DentalClaimsICD> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                // Handle any post-delete logic if needed
                StateHasChanged();
            }
        }
        // Update ResetForm method

        private void OnOccupationalInjuryChanged(bool value)
        {
            DentalClaims.IsOccupationalInjury = value;
            if (!value)
            {
                DentalClaims.OccupationalInjuryDetails = string.Empty;
            }
        }

        private void OnAutoAccidentChanged(bool value)
        {
            DentalClaims.IsAutoAccident = value;
            if (!value)
            {
                DentalClaims.AutoAccidentDetails = string.Empty;
            }
        }

        private void OnOtherAccidentChanged(bool value)
        {
            DentalClaims.IsOtherAccident = value;
            if (!value)
            {
                DentalClaims.OtherAccidentDetails = string.Empty;
            }
        }

        private void OnProsthesisChanged(bool value)
        {
            DentalClaims.IsProsthesis = value;
            if (!value)
            {
                DentalClaims.ReplacementReason = string.Empty;
                DentalClaims.PriorDate = null; // or DateTime.MinValue depending on your property type
            }
        }

        private void OnOrthodonticsTreatmentChanged(bool value)
        {
            DentalClaims.OrthodonticsTreatment = value;
            if (!value)
            {
                DentalClaims.OrthodonticsTreatmentServiceCommenced = string.Empty;
                DentalClaims.OrthodonticsTreatmentRemaining = string.Empty;
                DentalClaims.OrthodonticsTreatmentDate = null; // or DateTime.MinValue depending on your property type
            }
        }
        private void OnRadiographsChanged(bool value)
        {
            DentalClaims.HasRadiographs = value;
            if (!value)
            {
                DentalClaims.RadiographsDetails = string.Empty;
            }
        }

        private void OnBillToPatientChanged(bool value)
        {
            DentalClaims.BillToPatient = value;
            if (!value)
            {
                // Reset copay when unchecked
                DentalClaims.Copay = 0;
                DentalClaims.PatientCharges = 0;
                DentalClaims.PatientBalance = 0;
                DentalClaims.PatientPayments = 0;
                DentalClaims.PatientUncoveredAmount = 0;
            }
            StateHasChanged();
        }

        private bool ValidateServiceCharge(DentalClaimsCharges charge)
        {
            var errors = new List<string>();

            if (charge.Fee <= 0)
            {
                errors.Add("Fee must be greater than 0");
            }

            if (string.IsNullOrWhiteSpace(charge.Description))
            {
                errors.Add("Description is required");
            }

            if (string.IsNullOrWhiteSpace(charge.DOS))
            {
                errors.Add("DOS is required");
            }

            if (errors.Any())
            {
                foreach (var error in errors)
                {
                    Snackbar?.Add(error, Severity.Warning);
                }
                return false;
            }
            return true;
        }
    }
}