﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System.Threading;
using Markdig.Helpers;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using TeyaUIModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class CustomImmunizationAlert
    {
        [Inject] public IVaccineService VaccineService { get; set; }
        [Inject] public IFDBService FDBService { get; set; }
        [Inject] public ICustomImmunizationAlertService CustomImmunizationAlertService { get; set; }
        [Inject] private ILogger<CustomImmunizationAlert> _logger { get; set; }
        [Inject] private IStringLocalizer<CustomImmunizationAlert> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<CustomImmunizationAlerts>? CustomImmunizationAlertGrid;
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        private List<CustomImmunizationAlerts> customImmunizationAlerts = new();
        private List<CustomImmunizationAlerts> deleteAlertList = new();
        private List<CustomImmunizationAlerts> addList = new();
        public enum Source { CDC, FDB }

        private string selectedSource = Source.CDC.ToString();

        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        private string? gender = null; 

        // New field for source selection
        private string selectedVaccineName = string.Empty;
        private FDBVaccines? FDBSelectedVaccine;

        [Inject]
        private PatientService PatientService { get; set; } = default!;
        [Inject] IOrderSetService orderSetService { get; set; }


        public string VaccineName { get; set; }
        private Guid Id { get; set; }
        private Guid? OrgID { get; set; }
        private List<Vaccines> _details { get; set; } = new List<Vaccines>();
        private List<FDBVaccines> _fdbVaccines { get; set; } = new List<FDBVaccines>();
        private List<CompleteOrderSet> OrdersetList;
        private List<CompleteOrderSet> FilteredOrderset;
        private CompleteOrderSet selectedOrderset;
        private bool showNoResultsMessage;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                _details = await VaccineService.GetAllVaccinesDataAsync();
                _fdbVaccines = await FDBService.GetVaccines();

                // Check if patient is selected
                if (PatientService.PatientData != null && PatientService.PatientData.Id != Guid.Empty)
                {
                    // Patient is selected - use patient's organization
                    OrgID = PatientService.PatientData.OrganizationID;
                    _logger.LogInformation($"Patient selected, using patient organization: {OrgID}");
                }
                else
                {
                    // No patient selected - get organization by name and then get organization ID
                    var organization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);
                    if (organization != null && organization.Count > 0)
                    {
                        OrgID = organization[0].OrganizationId;
                        _logger.LogInformation($"No patient selected, using user organization: {OrgID}");
                    }
                    else
                    {
                        _logger.LogWarning($"No organization found with name: {User.OrganizationName}");
                        OrgID = null;
                    }
                }

                // Load order sets first
                OrdersetList = (await orderSetService.GetAllOrderSetAsync()).ToList();

                // Then load alerts
                await LoadAlertsAsync();

                // Force UI update after everything is loaded
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving immunization alert data");
                Snackbar?.Add("Error loading vaccine data. Please try again later.", Severity.Error);
            }
        }

        private async Task LoadAlertsAsync()
        {
            try
            {
                _logger.LogInformation("Loading all immunization alerts from database");
                var existingAlerts = await CustomImmunizationAlertService.GetActiveCustomImmunizationAlertsByOrganizationIdAsync(Id, OrgID, false);

                _logger.LogInformation($"Found {existingAlerts?.Count() ?? 0} total alerts in database");

                customImmunizationAlerts = existingAlerts?.ToList() ?? new List<CustomImmunizationAlerts>();

                // Add empty rows for UI display
                int emptyRowsNeeded = Math.Max(0, 9 - customImmunizationAlerts.Count);
                if (emptyRowsNeeded > 0)
                {
                    customImmunizationAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                        .Select(_ => new CustomImmunizationAlerts
                        {
                            Name = string.Empty,
                            Description = string.Empty,
                            WebReference = string.Empty,
                            OrderSet = string.Empty,
                            Gender = string.Empty
                        }));
                }

                // Force grid refresh if it exists
                if (CustomImmunizationAlertGrid != null)
                {
                    await CustomImmunizationAlertGrid.Refresh();
                }

                // Trigger UI update
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadAlertsAsync");
                customImmunizationAlerts = new List<CustomImmunizationAlerts>();
                Snackbar?.Add("Error loading immunization alerts. Please try again later.", Severity.Error);
            }
        }

        public void ActionCompletedHandler(ActionEventArgs<CustomImmunizationAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }
        private async Task<IEnumerable<string>> SearchOrderset(string searchTerm, CancellationToken cancellationToken)
        {
            if (searchTerm == null || string.IsNullOrWhiteSpace(searchTerm))
            {
                FilteredOrderset = OrdersetList;
                orderSet = null;
                showNoResultsMessage = false;
                StateHasChanged();
                var result = OrdersetList
                    .Select(temp => temp.orderSet.OrderSetName)
                    .Where(name => !string.IsNullOrEmpty(name))
                    .Distinct()
                    .ToList();
                return result;
            }
            else
            {
                var filtered = OrdersetList
                            .Where(temp => temp.orderSet.OrderSetName != null &&
                                       temp.orderSet.OrderSetName.Contains(searchTerm, StringComparison.InvariantCultureIgnoreCase))
                            .Select(temp => temp.orderSet.OrderSetName)
                            .ToList();

                if (!filtered.Any())
                {
                    showNoResultsMessage = true;
                    return new List<string> { "No orderset Found" };
                }

                showNoResultsMessage = false;
                return filtered;
            }
        }
        private void OnOrdersetSelected(string ordersetName)
        {
            orderSet = ordersetName;
            selectedOrderset = OrdersetList.FirstOrDefault(o => o.orderSet.OrderSetName == ordersetName);
            StateHasChanged();
        }
        public async Task ActionBeginHandler(ActionEventArgs<CustomImmunizationAlerts> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    _localizer["Confirm Delete"],
                    _localizer["Do you want to delete this entry?"],
                    yesText: _localizer["Yes"],
                    noText: _localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        protected async Task<IEnumerable<string>> SearchVaccinesData(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(value))
                return new List<string>();

            var searchResults = _details
                .Where(t => !string.IsNullOrEmpty(t.VaccineName) && t.VaccineName.Contains(value, StringComparison.OrdinalIgnoreCase))
                .Select(t => t.VaccineName)
                .Distinct()
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

        protected async Task<IEnumerable<FDBVaccines>> SearchFDBVaccine(string value, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(value) || value.Length < 2)
                return new List<FDBVaccines>();

            var searchResults = _fdbVaccines
                .Where(t => !string.IsNullOrEmpty(t.EVD_CVX_CD_DESC_SHORT) &&
                           t.EVD_CVX_CD_DESC_SHORT.Contains(value, StringComparison.OrdinalIgnoreCase))
                .ToList();

            cancellationToken.ThrowIfCancellationRequested();

            return searchResults;
        }

        private void OnFDBVaccineSelected(FDBVaccines vaccine)
        {
            if (vaccine != null)
            {
                FDBSelectedVaccine = vaccine;
                alertName = vaccine.EVD_CVX_CD_DESC_SHORT; // Set alertName when vaccine is selected
            }
        }

        private async Task SaveData()
        {
            try
            {
                if (addList.Count > 0)
                {
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await CustomImmunizationAlertService.AddCustomImmunizationAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await CustomImmunizationAlertService.UpdateCustomImmunizationAlertsListAsync(deleteAlertList, OrgID, false);
                }

                var existingAlerts = customImmunizationAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await CustomImmunizationAlertService.UpdateCustomImmunizationAlertsListAsync(existingAlerts, OrgID, false);
                }

                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["Immunization alerts saved successfully"], Severity.Success);

                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving immunization alert data");
                Snackbar.Add(_localizer["Failed to save immunization alert records"], Severity.Error);
            }
        }

        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            MudDialog?.Close();
        }

        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = null;
            selectedVaccineName = string.Empty;
            FDBSelectedVaccine = null;
        }

        private async Task AddNewAlert()
        {
            try
            {
                // Parse the string to Source enum
                if (!Enum.TryParse<Source>(selectedSource, out Source sourceType))
                {
                    Snackbar.Add(_localizer["Invalid source type"], Severity.Warning);
                    return;
                }

                // Set alertName based on selected source
                switch (sourceType)
                {
                    case Source.CDC:
                        if (string.IsNullOrEmpty(selectedVaccineName))
                        {
                            Snackbar.Add(_localizer["Please select a vaccine"], Severity.Warning);
                            return;
                        }
                        alertName = selectedVaccineName;
                        break;

                    case Source.FDB:
                        if (FDBSelectedVaccine == null)
                        {
                            Snackbar.Add(_localizer["Please select a vaccine"], Severity.Warning);
                            return;
                        }
                        alertName = FDBSelectedVaccine.EVD_CVX_CD_DESC_SHORT;
                        break;
                }

                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please select a vaccine"], Severity.Warning);
                    return;
                }
                if (string.IsNullOrEmpty(alertDescription))
                {
                    Snackbar.Add(_localizer["Please add a Description"], Severity.Warning);
                    return;
                }

                // Validate age bounds
                if (!ageLowerBound.HasValue || !ageUpperBound.HasValue)
                {
                    Snackbar.Add(_localizer["Please select age lower and upper bound"], Severity.Warning);
                    return;
                }

                if (ageUpperBound.Value == 0)
                {
                    Snackbar.Add(_localizer["Age upper bound should be minimum 1"], Severity.Warning);
                    return;
                }
                if (string.IsNullOrEmpty(gender))
                {
                    Snackbar.Add(_localizer["Please select a gender"], Severity.Warning);
                    return;
                }

                var emptyRow = customImmunizationAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new CustomImmunizationAlerts();
                    customImmunizationAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrdersetId = selectedOrderset?.orderSet?.Id ?? Guid.Empty;
                emptyRow.OrderSet = orderSet ?? string.Empty;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;

                addList.Add(emptyRow);

                if (CustomImmunizationAlertGrid != null)
                {
                    await CustomImmunizationAlertGrid.Refresh();
                }

                ResetInputFields();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new immunization alert");
                Snackbar.Add(_localizer["Failed to add immunization alert"], Severity.Error);
            }
        }

        // Public method to refresh alerts - can be called from parent component
        public async Task RefreshAlertsAsync()
        {
            await LoadAlertsAsync();
        }

        // Method to check if alerts are loaded
        public bool HasAlertsLoaded => customImmunizationAlerts?.Any(a => !string.IsNullOrEmpty(a.Name)) == true;
    }
}