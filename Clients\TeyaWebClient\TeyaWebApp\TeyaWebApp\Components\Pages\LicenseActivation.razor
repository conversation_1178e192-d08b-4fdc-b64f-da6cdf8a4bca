﻿@page "/LicenseActivation"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "LicenseActivationAccessPolicy")]
@inject IProductLicenseService licenseService
@layout Admin
@using TeyaWebApp.Components.Layout
@using TeyaUIViewModels.ViewModel
@using TeyaUIModels.Model
@using TeyaWebApp.ViewModel
@using MudBlazor

<GenericCard Heading="License Activation">
    @if (licenses != null && licenses.Any())
    {
        <MudTable Items="@licenses" Hover="true" Breakpoint="Breakpoint.Sm">
            <HeaderContent>
                <MudTh>Name</MudTh>
                <MudTh>Description</MudTh>
                <MudTh>Activated</MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="Name">@context.ProductName</MudTd>
                <MudTd DataLabel="Description">@context.Description</MudTd>
                <MudTd DataLabel="Activated">
                    <MudCheckBox @bind-Value="@context.IsLicenseActivated"
                                 @onchange="(e) => UpdateProductAccessLocally(context, ((ChangeEventArgs)e).Value)">
                    </MudCheckBox>
                </MudTd>
            </RowTemplate>
        </MudTable>
        <MudGrid>
            <MudItem xs="12" class="d-flex justify-end" style="margin-top: 16px;">
                <MudButton Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Save"
                           Color="Color.Info" Size="Size.Small" OnClick="SaveAllMemberAccess">
                    Save
                </MudButton>
            </MudItem>
        </MudGrid>
    }
</GenericCard>

