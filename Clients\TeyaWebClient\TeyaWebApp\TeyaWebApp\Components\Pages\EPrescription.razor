﻿@page "/EPrescription"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor

<MudPaper Class="pa-4 mb-4 global-font">
    <div class="layout-container">
        <!-- Left Column -->
        <div class="column">
            <MudTextField @bind-Value="PatientName" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Patient Name"]" />
            <MudTextField @bind-Value="PatientAddress" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Patient Address"]" />
            <MudTextField @bind-Value="DoctorName" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Doctor Name"]" />
            <MudTextField @bind-Value="PharmacyName" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Pharmacy Name"]" />
            <MudTextField @bind-Value="PharmacyAddress" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Pharmacy Address"]" />
            <MudTextField @bind-Value="Comments" Lines="4" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Comments"]" />
            <MudSelect T="string" @bind-Value="SupervisingProvider" Variant="Variant.Outlined" Margin="Margin.Dense" Label="@Localizer["Provider"]">
                @if (Providers != null)
                {
                    @foreach (var provider in Providers)
                    {
                        <MudSelectItem T="string" Value="@provider">@provider</MudSelectItem>
                    }
                }
            </MudSelect>
        </div>

        <!-- Divider -->
        <div class="vertical-divider"></div>

        <!-- Right Column -->
        <div class="column">
            <!-- Section 1: Buttons -->
            <div class="section-box">
                <MudGrid Spacing="2">
                    <MudItem xs="12" sm="6">
                        <MudButton Color="Color.Primary" OnClick="LinkPharmacy" Variant="Variant.Filled" FullWidth="true" Style="padding: 6px 6px;">
                            @Localizer["Link Pharmacy"]
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudButton Color="Color.Primary" OnClick="RxExternalHistory" Variant="Variant.Filled" FullWidth="true" Style="padding: 6px 6px;">
                            @Localizer["Rx External History"]
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudButton Color="Color.Primary" OnClick="PatientHub" Variant="Variant.Filled" FullWidth="true" Style="padding: 6px 6px;">
                            @Localizer["Patient Hub"]
                        </MudButton>
                    </MudItem>
                    <MudItem xs="12" sm="6">
                        <MudButton Color="Color.Primary" OnClick="RxEligibility" Variant="Variant.Filled" FullWidth="true" Style="padding: 6px 6px;">
                            @Localizer["Rx Eligibility"]
                        </MudButton>
                    </MudItem>
                </MudGrid>
            </div>

            <!-- Section 2: E-Prescription Type -->
            <div class="section-box">
                <MudText Class="section-label">@Localizer["E-Prescription Type"]</MudText>
                <MudRadioGroup T="string" @bind-Value="SelectedEprescriptionType" Orientation="Row">
                    <MudRadio T="string" Option="New">@Localizer["New"]</MudRadio>
                    <MudRadio T="string" Option="RefillRx">@Localizer["Refill Rx"]</MudRadio>
                </MudRadioGroup>
            </div>

            <!-- Section 3: Response -->
            <div class="section-box">
                <MudText Class="section-label">@Localizer["Response"]</MudText>
                <MudRadioGroup T="string" @bind-Value="SelectedResponse" Orientation="Row">
                    <MudRadio T="string" Option="Approved">@Localizer["Approved"]</MudRadio>
                    <MudRadio T="string" Option="Denied">@Localizer["Denied"]</MudRadio>
                    <MudRadio T="string" Option="Denied/New Rx to follow">@Localizer["Denied/New Rx to follow"]</MudRadio>
                </MudRadioGroup>
            </div>

            <!-- Section 4: Refill Details -->
            <div class="section-box">
                <MudText Class="section-label">@Localizer["Refill Details"]</MudText>
                <MudSelect T="int" @bind-Value="SelectedRefill" Variant="Variant.Outlined" Margin="Margin.Dense">
                    @foreach (var number in RefillOptions)
                    {
                        <MudSelectItem T="int" Value="@number">@number</MudSelectItem>
                    }
                </MudSelect>
            </div>
        </div>
    </div>

    <div class="prescription-note">
        <p><strong>@Localizer["Prescription Medication"]</strong></p>
        <Prescription></Prescription>
    </div>

    <div class="button-group-spacing">
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="ShowPreviewRX">
            @Localizer["Show Preview RX"]
        </MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SendEPrescription">
            @Localizer["Send E Prescription"]
        </MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveAsPDF">
            @Localizer["Save as Pdf"]
        </MudButton>
    </div>
</MudPaper>