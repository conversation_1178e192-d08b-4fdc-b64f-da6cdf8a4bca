﻿@page "/QualityMeasure"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using System.Text
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@using Syncfusion.Blazor.Grids
@using System.Text.RegularExpressions



<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenNewDialogBox" Class="mx-2 mt-2">@Localizer["Report"]</MudButton>

<MudDialog @ref="_qualityMeasureDialog" Style="width: 95vw; max-width: 1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size: 1rem; font-weight: 600;">
            Quality Measure Report
        </MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges"
                       Style="margin: -4px; position: absolute; right: 16px; top: 16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin: -12px; display: flex; height: 70vh;">
            <!-- Left Section - CDSS Alert Measures -->
            <div style="flex: 2; padding: 20px; border-right: 1px solid #E0E0E0; overflow-y: auto;">
                <MudText Typo="Typo.h6" Style="margin-bottom: 16px;">Measure Defination</MudText>
                <SfGrid @ref="MeasuresGrid"
                        DataSource="@Measures"
                        AllowPaging="true"
                        PageSettings-PageSize="10"
                        GridLines="GridLine.Both"
                        TValue = "Measure"
                        AllowAdding = "true"                  
                        Style="font-size: 0.85rem; height: calc(100% - 40px); margin-top: 24px; width: 100%;">
                        
                    <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" Mode="EditMode.Normal"></GridEditSettings>

                    <GridPageSettings PageSize="10"></GridPageSettings>

                    <GridEvents OnActionBegin="ActionBeginHandlerAsync" TValue="Measure"></GridEvents>

                    <GridColumns>
                        <GridColumn Field="Id" IsPrimaryKey="true" Visible="false"></GridColumn>
                        <GridColumn Field="MeasureTitle" HeaderText="Measure Title" Width="200" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="MeasureDescription" HeaderText="Measure Description" Width="200" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="MeasureType" HeaderText="Measure Type" Width="100" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="MeasureNumerator" HeaderText="Measure Numerator" Width="200" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="MeasureDenominator" HeaderText="Measure Denominator" Width="200" TextAlign="TextAlign.Center"></GridColumn>
                        <GridColumn Field="CDSS" HeaderText="CDSS" Width="80" TextAlign="TextAlign.Center">
                            <Template Context="context">
                                @{
                                    var measure = (Measure)context;
                                }
                                <MudText>@measure.CDSS</MudText>
                            </Template>
                        </GridColumn>
                        <GridColumn Field="OrderSetLinked" HeaderText="Order Set Linked" Width="100" TextAlign="TextAlign.Center"></GridColumn>

                        <GridColumn HeaderText="@Localizer["Actions"]" Width="30" TextAlign="TextAlign.Center">
                            <GridCommandColumns>
                                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat"})" />
                            </GridCommandColumns>
                        </GridColumn>
                    </GridColumns>
                </SfGrid>

            </div>

            <!-- Right Section - Search/Add Measure -->
            <div style="flex: 1; padding: 20px; overflow-y: auto;">
                <MudText Typo="Typo.h6" Style="margin-bottom: 16px;">Search Measure</MudText>

                <div style="display: flex; gap: 8px; margin-bottom: 16px;">
                    <MudTextField @bind-Value="SearchTerm" Label="Measure"
                                  Variant="Variant.Outlined" Immediate="true" Style="flex-grow: 1;height: 45px;" Lines="1" />
                    <MudButton Variant="Variant.Filled" Color="Color.Primary"
                               OnClick="SuggestMeasures" Style="height: 40px; min-width: 85px; margin-top:10px;">
                        SEARCH
                    </MudButton>
                </div>
                <MudText Typo="Typo.h6" Style="margin-bottom: 16px;">Add Measure</MudText>


                <div style="display: flex; flex-direction: column; gap: 16px;">
                    <MudTextField @bind-Value="NewMeasure.MeasureTitle" Label="Measure Name"
                                  Variant="Variant.Outlined" Style="height: 45px" />
                    <div style="display:flex; flex-direction: row; align-items: center;">
                        <MudTextField @bind-Value="NewMeasure.MeasureNumerator" Label="Measure Numerator"
                                      Variant="Variant.Outlined" style="margin-right: 16px;height: 45px" />
                        <MudTextField @bind-Value="NewMeasure.MeasureDenominator" Label="Measure Denominator"
                                      Variant="Variant.Outlined" Style="height: 45px;" />
                    </div>

                    <MudTextField @bind-Value="NewMeasure.MeasureDescription" Label="Measure Description"
                                  Variant="Variant.Outlined" Lines="1" Style="height: 45px;" />



                    <div style="display:flex; flex-direction: row; align-items: center;">
                        <MudSelect @bind-Value="NewMeasure.CDSS" Label="CDSS" Variant="Variant.Outlined"
                                   Class="small-select" style="margin-right: 16px;">
                            @foreach (var state in _states)
                            {
                                <MudSelectItem Value="@state" Class="small-select">@state</MudSelectItem>
                            }
                        </MudSelect>


                        <MudTextField @bind-Value="NewMeasure.MeasureType" Label="MeasureType"
                                      Variant="Variant.Outlined" Style="height: 45px" />
                    </div>



                    <MudTextField @bind-Value="NewMeasure.OrderSetLinked" Label="Order Set Linked"
                                  Variant="Variant.Outlined" Style="height: 45px" />

                </div>

                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="AddMeasures"
                    Style="min-width: 120px; height: 40px;">
                        ADD
                    </MudButton>
                </div>
            </div>
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 12px; padding: 16px 24px; border-top: 1px solid #E0E0E0; margin-top: 24px;">
            <MudButton Color="Color.Secondary"
            Variant="Variant.Outlined"
            OnClick="CancelChanges"
            Dense="true"
            Style="min-width: 120px; height: 40px; font-weight: 600;">
                @Localizer["Cancel"]
            </MudButton>
            <MudButton Color="Color.Primary"
            Variant="Variant.Filled"
            OnClick="SaveChanges"
            Dense="true"
            Style="min-width: 120px; height: 40px; font-weight: 600;">
                @Localizer["Save"]
            </MudButton>
        </div>
    </DialogContent>
</MudDialog>

<style>
    ::deep .e-grid .e-headercell {
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6 !important;
    border-bottom: 2px solid #dee2e6 !important;
    padding: 8px !important;
    font-weight: 600;
    }

    ::deep .e-grid .e-rowcell {
    border-right: 1px solid #dee2e6 !important;
    padding: 8px !important;
    }

    ::deep .e-grid .e-row {
    border-bottom: 1px solid #dee2e6 !important;
    }

    .small-select .mud-input {
        height: 45px;
        font-size: 0.85rem; /* Optional for visual consistency */
    }


    

</style>

