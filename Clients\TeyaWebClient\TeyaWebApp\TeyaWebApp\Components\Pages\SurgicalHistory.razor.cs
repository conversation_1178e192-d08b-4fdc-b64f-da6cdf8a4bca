﻿using Syncfusion.Blazor.Popups;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using System.Threading;
using System.Linq;
using static MudBlazor.Icons.Custom;
using Unity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;
using Microsoft.Azure.Amqp.Framing;

namespace TeyaWebApp.Components.Pages
{
    public partial class SurgicalHistory
    {
        [Inject] public IICDService _ICDService { get; set; }
        [Inject] public ISurgicalService _SurgicalService { get; set; }
        [Inject] private ILogger<SurgicalHistory> _logger { get; set; }
        [Inject] private IStringLocalizer<SurgicalHistory> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }

        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IFDBService FDBService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }

        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private MudDialog _surgicalhistory;
        private string _Surgery;

        private List<Surgical> surgicalhistory { get; set; }
        public enum Source { CMS,FDB }
        private string selectedDatabase = Source.CMS.ToString();
        public string ICDName { get; set; }

        private SfRichTextEditor RichTextEditor;
        private List<ICDCode> _icdCodes { get; set; } = new List<ICDCode>();
        private List<FDB_ICD> fdb_ICD { get; set; } = new List<FDB_ICD>();


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" },
            new ToolbarItemModel() { Name = "close" }
        };

        public SfGrid<Surgical> SurgeryGrid { get; set; }

        private Guid PatientId { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        private List<Surgical> deletesurgerylist { get; set; } = new List<Surgical>();
        private List<Surgical> AddList = new();


        protected override async Task OnInitializedAsync()
        {
            // Phase 1: Load minimal data for initial render
            PatientId = PatientID;
            ManualContent = Data;
            OrgID = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            surgicalhistory = await _SurgicalService.GetSurgeryByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }



        /// <summary>
        /// To show the data in Rich Text Editor
        /// </summary>
        private void UpdateEditorContent()
        {
            editorContent = string.Join("<p>", surgicalhistory
                .OrderByDescending(s => s.CreatedDate)
                .Select(s => $"<strong>{s.CreatedDate.ToString("dd-MM-yyyy")}</strong> - {s.Surgery}"));
        }

        private void OnDatabaseChanged(string newDatabase)
        {
            selectedDatabase = newDatabase;
            ICDName = null; // Reset the drug name when database changes
            StateHasChanged(); // Ensure UI updates
        }

        /// <summary>
        /// Open Dailog
        /// </summary>
        /// <returns></returns>
        private async Task OpenNewDialogBox()
        {
            await _surgicalhistory.ShowAsync();
        }

        /// <summary>
        /// close Dailog
        /// </summary>
        /// <returns></returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _surgicalhistory.CloseAsync();
        }

        /// <summary>
        /// Search Function to get ICD Codes and Description 
        /// </summary>
        /// <param name="value"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        private CancellationTokenSource _searchICDCancellationTokenSource;
        private CancellationTokenSource _searchFDBCancellationTokenSource;

        protected async Task<IEnumerable<string>> SearchICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return Enumerable.Empty<string>();

            if (selectedDatabase == Source.CMS.ToString())
            {
                return await SearchCMSICDCodes(searchTerm, cancellationToken);
            }
            else if (selectedDatabase == Source.FDB.ToString())
            {
                return await SearchFDBICDCodes(searchTerm, cancellationToken);
            }

            return Enumerable.Empty<string>();
        }

        private async Task<IEnumerable<string>> SearchCMSICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            // Cancel previous search if still running
            _searchICDCancellationTokenSource?.Cancel();
            _searchICDCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine the external cancellation token with our local one
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchICDCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with the current search term
                var results = await _ICDService.GetAllICDCodesBySearchTermAsync(searchTerm);

                return results
                    .Select(icd => $"{icd.Code} - {icd.Description ?? "No description available"}")
                    .ToList();
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search CMS ICD codes");
                return Enumerable.Empty<string>();
            }
        }

        private async Task<IEnumerable<string>> SearchFDBICDCodes(string searchTerm, CancellationToken cancellationToken)
        {
            // Cancel previous search if still running
            _searchFDBCancellationTokenSource?.Cancel();
            _searchFDBCancellationTokenSource = new CancellationTokenSource();

            try
            {
                // Combine the external cancellation token with our local one
                var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    cancellationToken,
                    _searchFDBCancellationTokenSource.Token);

                // Add debounce delay (300ms)
                await Task.Delay(300, linkedCts.Token);

                // Call service with the current search term
                var results = await FDBService.GetICDBySearchTerm(searchTerm);

                return results
                    .Where(icd => icd.ICD_CD_TYPE == "06" || icd.ICD_CD_TYPE == "05") // Maintain your filter
                    .Select(icd => $"{icd.ICD_CD} - {icd.ICD_DESC ?? ""}")
                    .ToList();
            }
            catch (TaskCanceledException)
            {
                return Enumerable.Empty<string>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to search FDB ICD codes");
                return Enumerable.Empty<string>();
            }
        }

        /// <summary>
        /// Add new Surgery and update it to the database
        /// </summary>
        private async void AddNewSurgery()
        {
            if (DateTime.Now.Date < DateTime.Now.Date)
            {
                
                Snackbar.Add(Localizer["Future.DateError"], Severity.Warning);
                return;
            }

            var newSurgery = new Surgical
            {
                SurgeryId = Guid.NewGuid(),
                PatientId = PatientId,
                PCPId = Guid.Parse(User.id),
                OrganizationId = OrgID ?? Guid.Empty,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = Guid.Parse(User.id),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Surgery = ICDName,
                IsActive = true,
            };

            AddList.Add(newSurgery);

            surgicalhistory.Add(newSurgery);
            await SurgeryGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Clear the fields for closure
        /// </summary>
        private void ResetInputFields()
        {
            ICDName = string.Empty;
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        /// <summary>
        /// Save removed rows locally in SFgrid
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<Surgical> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedSurgery = args.Data as Surgical;
                var existingItem = AddList.FirstOrDefault(v => v.SurgeryId == deletedSurgery.SurgeryId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deletesurgerylist.Add(args.Data);
                }
            }
        }

        /// <summary>
        /// To 
        /// </summary>
        /// <param name="args"></param>
        public async Task ActionBeginHandler(ActionEventArgs<Surgical> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {


                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }

             
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.CreatedDate.Date > DateTime.Now.Date)
                {
                    Snackbar.Add(Localizer["Future.DateError"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        ///  Save function to save the data in database (Fron-ent 'Save' Button)
        /// </summary>
        /// <returns></returns>
        private async Task SaveData()
        {
            bool? saveResult = await DialogService.ShowMessageBox(
                Localizer["Confirm Save"],
                Localizer["Are you sure you want to save these changes?"],
                yesText: Localizer["Yes"],
                noText: Localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }
            if (AddList.Count != 0)
            {
                await _SurgicalService.AddSurgeryAsync(AddList, OrgID, Subscription);
            }
            await _SurgicalService.UpdateSurgeryListAsync(deletesurgerylist, OrgID, Subscription);
            await _SurgicalService.UpdateSurgeryListAsync(surgicalhistory, OrgID, Subscription);
            deletesurgerylist.Clear();
            AddList.Clear();
            //UpdateEditorContent();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            await InvokeAsync(StateHasChanged);
            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            CloseNewDialogBox();
        }

        /// <summary>
        /// To Undo Changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelData()
        {
            deletesurgerylist.Clear();
            AddList.Clear();
            surgicalhistory = await _SurgicalService.GetSurgeryByIdAsyncAndIsActive(PatientId, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Update Value in ICD Name List
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private async Task OnICDNameChanged(string value)
        {
            ICDName = value;
            StateHasChanged();
        }



        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = (surgicalhistory != null && surgicalhistory.Any())
                ? string.Join(" ",
                    surgicalhistory
                        .Where(s => !string.IsNullOrEmpty(s.Surgery))
                        .OrderByDescending(s => s.CreatedDate)
                        .Select(s => $"<ul><li style='margin-left: 20px;'><b>{s.CreatedDate:yyyy-MM-dd}</b> - {s.Surgery}</li></ul>"))
                : string.Empty;

            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }


        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}