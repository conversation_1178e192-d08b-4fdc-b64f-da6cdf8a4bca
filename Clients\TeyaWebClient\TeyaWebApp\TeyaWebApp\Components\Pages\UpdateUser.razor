﻿@page "/updateuser/{userId}"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "updateuserAccessPolicy")]
@using System.Text.Json
@inject GraphApiService customAuthenticationService
@inject NavigationManager Navigation
@using TeyaWebApp.Components.Layout
@layout Admin
@using TeyaWebApp.TeyaAIScribeResource
@using TeyaWebApp.ViewModel
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

@if (isLoading)
{
    <MudText Typo="Typo.body1">@Localizer["LoadingUserData"]</MudText>
}
else if (user == null)
{
    <MudText Typo="Typo.body1" Color="Color.Error">@Localizer["UserDataNotLoaded"]</MudText>
}
else
{
    <GenericCard Heading="@Localizer["EditUser"]">
        <EditForm Model="@user" OnValidSubmit="SaveUserDetails">
            <MudGrid>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["DisplayName"]" @bind-Value="user.DisplayName" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["GivenName"]" @bind-Value="user.GivenName" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["Surname"]" @bind-Value="user.Surname" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["Email"]" @bind-Value="user.Mail" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["UserType"]" @bind-Value="user.UserType" Variant="Variant.Outlined" ReadOnly="true" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["JobTitle"]" @bind-Value="user.JobTitle" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["CompanyName"]" @bind-Value="user.CompanyName" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["Department"]" @bind-Value="user.Department" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["OfficeLocation"]" @bind-Value="user.OfficeLocation" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["MobilePhone"]" @bind-Value="user.MobilePhone" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["StreetAddress"]" @bind-Value="user.StreetAddress" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["City"]" @bind-Value="user.City" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["State"]" @bind-Value="user.State" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["PostalCode"]" @bind-Value="user.PostalCode" Variant="Variant.Outlined" />
                </MudItem>
                <MudItem xs="12" sm="6" md="4">
                    <MudTextField Label="@Localizer["Country"]" @bind-Value="user.Country" Variant="Variant.Outlined" />
                </MudItem>
            </MudGrid>

            <MudButton Color="Color.Primary" OnClick="async () => await SaveUserDetails()" Type="Submit">@Localizer["Save"]</MudButton>
        </EditForm>
    </GenericCard>
}
