﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.Graph.Models;

namespace TeyaWebApp.Components.Pages
{
    public partial class PhysicalExamination : ComponentBase
    {
        [Inject] public IPhysicalService PhysicalService { get; set; }
        
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }

        [Inject] private IStringLocalizer<PhysicalExamination> localizer { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private List<Physicalexamination> physicalexamination { get; set; }
        private List<Physicalexamination> AddList = new();
        private List<Physicalexamination> DeleteList = new();
        public SfGrid<Physicalexamination> PhysicalExaminationGrid { get; set; }
        public SfRichTextEditor RichTextEditor { get; set; }
        private MudDialog _physicalExamination;
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
        private string editorContent;
        private bool add = false;


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" },
            new ToolbarItemModel() { Name = "close" }
        };

        /// <summary>
        /// Initializes the component asynchronously and loads patient past results data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            
            ManualContent = Data;
            OrgID = OrgId;
            physicalexamination = await PhysicalService.GetExaminationByIdAsyncAndIsActive(PatientId, OrgID, false);
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        /// <summary>
        /// Loads past results data asynchronously for the given patient.
        /// </summary>
        private async Task LoadDataAsync()
        {
            
            physicalexamination = await PhysicalService.GetExaminationByIdAsyncAndIsActive(PatientId, OrgID, false);
           
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Opens the add task dialog for adding new past result records.
        /// </summary>
        private async Task NewDialogBox()
        {
            await _physicalExamination.ShowAsync();
        }

        /// <summary>
        /// Handles the completion of actions such as Add, Save, and Delete in the past result grid.
        /// </summary>
        /// <param name="args">Action event arguments containing the details of the operation.</param>
        private void ActionCompletedHandler(ActionEventArgs<Physicalexamination> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedexamination = args.Data as Physicalexamination;
                    var existingItem = AddList.FirstOrDefault(s => s.ExaminationId == deletedexamination.ExaminationId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedexamination.UpdatedBy = Guid.Parse(User.id);
                        deletedexamination.UpdatedDate = DateTime.Now;
                        deletedexamination.IsActive = false;
                        DeleteList.Add(deletedexamination);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.ExaminationId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = OrgID ?? Guid.Empty;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.IsActive = true;
                add = true;
            }
        }

        public async Task ActionBeginHandler(ActionEventArgs<Physicalexamination> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    localizer["Confirm Delete"],
                    localizer["Do you want to delete this entry?"],
                    yesText: localizer["Yes"],
                    noText: localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedexaminations = args.Data;
                        if (addedexaminations != null)
                        {
                            AddList.Add(addedexaminations);
                        }
                    }
                    add = false;
                }
                if (!string.IsNullOrEmpty(args.Data.Tester) && !IsAlphabeticWithSpaces(args.Data.Tester))
                {
                    Snackbar.Add(localizer["Tester Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.Hemangioma) && !IsAlphabeticWithSpaces(args.Data.Hemangioma))
                {
                    Snackbar.Add(localizer["Hemangioma Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.VascularMalformation) && !IsAlphabeticWithSpaces(args.Data.VascularMalformation))
                {
                    Snackbar.Add(localizer["Vascular Malformation Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (!string.IsNullOrEmpty(args.Data.Skin) && !IsAlphabeticWithSpaces(args.Data.Skin))
                {
                    Snackbar.Add(localizer["Skin Location field should only contain alphabets"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                if (args.Data.CreatedDate.Date > DateTime.Now.Date)
                {
                    Snackbar.Add(@localizer["Date cannot be in the future"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }

        /// <summary>
        /// Saves the changes made to the past results records by adding new entries and updating existing ones.
        /// </summary>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await PhysicalService.AddExaminationAsync(AddList, OrgID, false);
            }
            await PhysicalService.UpdateExaminationListAsync(DeleteList, OrgID, false);
            await PhysicalService.UpdateExaminationListAsync(physicalexamination, OrgID, false);
            await LoadDataAsync();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            AddList.Clear();
            DeleteList.Clear();
            await _physicalExamination.CloseAsync();
        }

        /// <summary>
        /// Cancels changes and reloads the original past results data.
        /// </summary>
        private async Task CancelData()
        {
            await LoadDataAsync();
            await _physicalExamination.CloseAsync();
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(localizer["BackdropDisabledMessage"], Severity.Info);
        }
        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s/,.\\\(\)-]+$");
        }

        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = physicalexamination != null
                ? string.Join(" ", physicalexamination
                    .Where(display => !string.IsNullOrEmpty(display.Skin) || !string.IsNullOrEmpty(display.Tester) || !string.IsNullOrEmpty(display.Hemangioma))
                    .OrderByDescending(display => display.CreatedDate)
                    .Select(display => $"<ul><li style='margin-left: 20px;'><b>{display.CreatedDate:yyyy-MM-dd}</b> - Skin: {display.Skin}, " +
                            $"Tester: {display.Tester}, Hemangioma: {display.Hemangioma}</li></ul>"))
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }


        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}