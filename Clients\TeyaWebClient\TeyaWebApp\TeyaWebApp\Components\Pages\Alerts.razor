﻿@using TeyaWebApp.TeyaAIScribeResources
@using System.Collections.Generic
@inject IDiagnosticImagingService DiagnosticImagingService
@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenCustomAlertDialogAsync">@Localizer["Lab Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenCustomImmunizationAlertDialogAsync">@Localizer["Immunization Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenCustomProcedureAlertDialogAsync">@Localizer["Procedure Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenDIAlertDialogAsync">@Localizer["DI Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenDXAlertDialogAsync">@Localizer["DX Alerts"]</MudButton>
<MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="OpenPatientSpecificAlertDialogAsync">@Localizer["PatientSpecific Alerts"]</MudButton>

<MudContainer Class="mt-4">
    <MudPaper Elevation="3" Class="pa-4">
        <MudText Typo="Typo.h4" Class="mb-4 font-weight-bold fw-bold">@Localizer["Clinical Decision Support System"]</MudText>

        <MudGrid Class="mb-4" AlignItems="Center" Justify="Justify.SpaceBetween">
            <MudItem xs="12" md="6" Class="d-flex align-end">
                <MudTextField T="string"
                              Label="@Localizer["Search Alerts"]"
                              @bind-Value="searchString"
                              Adornment="Adornment.End"
                              AdornmentIcon="@Icons.Material.Filled.Search"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              Style="background-color: white; border-radius: 8px;"
                              Immediate="true"
                              Class="w-100" />
            </MudItem>
            <MudItem xs="12" md="6" Class="d-flex justify-end align-items-end">
                <MudSelect T="string"
                           Label="@Localizer["Filter By Severity"]"
                           @bind-Value="selectedSeverity"
                           Variant="Variant.Outlined"
                           Margin="Margin.Dense"
                           Style="background-color: white; border-radius: 8px; min-width: 200px;">
                    <MudSelectItem Value="@("All")">@Localizer["AllSeverities"]</MudSelectItem>
                    <MudSelectItem Value="@("High")">@Localizer["High"]</MudSelectItem>
                    <MudSelectItem Value="@("Medium")">@Localizer["Medium"]</MudSelectItem>
                    <MudSelectItem Value="@("Low")">@Localizer["Low"]</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>

        <MudCard>
            @if (!FilteredAlerts.Any())
            {
                <MudAlert Severity="Severity.Info" Class="my-4 mx-4">
                    @Localizer["NoAlertsFound"]
                </MudAlert>
            }
            else
            {
                <MudDataGrid Items="@FilteredAlerts"
                             Filterable="false"
                             SortMode="SortMode.Multiple"
                             Hover="true"
                             Dense="true">
                    <Columns>
                        <PropertyColumn Property="x => x.CreatedDate" Title="@Localizer["Date"]" Sortable="true" Format="MM/dd/yyyy" />
                        <PropertyColumn Property="x => x.PatientName" Title="@Localizer["Patient"]" Sortable="true" />
                        <PropertyColumn Property="x => x.AlertType" Title="@Localizer["AlertType"]" Sortable="true" />
                        <PropertyColumn Property="x => x.Description" Title="@Localizer["Reason"]" />
                        <TemplateColumn Title="@Localizer["Severity"]" Sortable="true" SortBy="x => x.Severity">
                            <CellTemplate>
                                @{
                                    var alert = context.Item;
                                    var color = alert.Severity switch
                                    {
                                        "High" => Color.Error,
                                        "Medium" => Color.Warning,
                                        "Low" => Color.Info,
                                        _ => Color.Default
                                    };
                                }
                                <MudChip Color="@color" Size="Size.Small" Class="mr-2">@alert.Severity</MudChip>
                            </CellTemplate>
                        </TemplateColumn>
                        <TemplateColumn Title="@Localizer["Actions"]">
                            <CellTemplate>
                                @{
                                    var alert = context.Item;
                                }
                                <MudTooltip Text="@Localizer["Dismiss"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                   Color="Color.Error"
                                                   Size="Size.Small"
                                                   OnClick="@(() => DismissAlert(alert))" />
                                </MudTooltip>
                            @if (alert.AlertType == "Configured Immunization Alert" ||
                                 alert.AlertType == "Configured Medication Alert" ||
                                alert.AlertType == "Configured Procedure Alert" ||
                                alert.AlertType == "Configured Lab Alert" ||
                                  alert.AlertType == "Configured Patient Specific" ||
                                 alert.AlertType == "Configured DI Alert")
                            {
                                <MudTooltip Text="@Localizer["Add Orderset"]">
                                    <MudIconButton Icon="@Icons.Material.Filled.MedicalInformation"
                                                   Color="Color.Info"
                                                   Size="Size.Small"
                                                   OnClick="@(() => ViewOrderSet(alert))" />
                                </MudTooltip>
                            }
                            </CellTemplate>
                        </TemplateColumn>
                    </Columns>
                    <PagerContent>
                        <MudDataGridPager />
                    </PagerContent>
                </MudDataGrid>
            }
        </MudCard>
    </MudPaper>
</MudContainer>
@if (selectedAlert != null)
{
    <MudDialog @bind-IsVisible="dialogVisible" Options="dialogOptions">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.MedicalServices" Class="mr-2" />
                @Localizer["AlertDetails"]
            </MudText>
        </TitleContent>
        <DialogContent>
            <MudPaper Class="pa-4" Elevation="0">
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["PatientLabel"]</strong> @selectedAlert.PatientName</MudText>
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["AlertTypeLabel"]</strong> @selectedAlert.AlertType</MudText>
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["SeverityLabel"]</strong> @selectedAlert.Severity</MudText>
                        <MudDivider Class="my-4" />
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["ProblemDescriptionLabel"]</strong></MudText>
                        <MudText Class="mb-4">@selectedAlert.Description</MudText>
                        <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["RecommendedActionLabel"]</strong></MudText>
                        <MudText>@selectedAlert.Solution</MudText>

                        @if (selectedAlert.AlertType == Localizer["DrugInteraction"])
                        {
                            <MudDivider Class="my-4" />
                            <MudText Typo="Typo.subtitle1" Class="mb-2"><strong>@Localizer["InteractingMedicationsLabel"]</strong></MudText>
                            <MudList T="string" Dense="true">
                                @foreach (var med in selectedAlert.AdditionalInfo.Split(","))
                                {
                                    <MudListItem Icon="@Icons.Material.Filled.Medication" Text="@med.Trim()" />
                                }
                            </MudList>
                        }
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(() => dialogVisible = false)" Color="Color.Default">@Localizer["Close"]</MudButton>
            <MudButton OnClick="@(() => DismissAlert(selectedAlert))" Color="Color.Primary">@Localizer["MarkAsReviewed"]</MudButton>
        </DialogActions>
    </MudDialog>

}

