﻿@page "/SocialHistory"
@using Microsoft.AspNetCore.Authorization
@attribute [Authorize]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject HttpClient Http
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Grids

<div class="description-container">
    @if (!isEditing)
    {
        <div class="description-box @(string.IsNullOrEmpty(editorContent) ? "empty" : "")"
             @onclick="StartEditing">
            <div class="description-content">
                @((MarkupString)editorContent)
            </div>
        </div>
    }
    else
    {
        <div class="editor-container">
            <SfRichTextEditor SaveInterval="saveInterval" Value="@editorContent" @ref="RichTextEditor"
                              ValueChanged="@((string newValue)=>HandelRichTextChange(newValue))">
                <RichTextEditorToolbarSettings Items="@Tools">
                    <RichTextEditorCustomToolbarItems>
                        <RichTextEditorCustomToolbarItem Name="Symbol">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.ModeEditOutline"
                                               Size="Size.Small"
                                               OnClick="OpenAddTaskDialog" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                        <RichTextEditorCustomToolbarItem Name="close">
                            <Template>
                                <MudIconButton Icon="@Icons.Material.Filled.Close"
                                               Size="Size.Small"
                                               OnClick="CloseRTE" />
                            </Template>
                        </RichTextEditorCustomToolbarItem>
                    </RichTextEditorCustomToolbarItems>
                </RichTextEditorToolbarSettings>
            </SfRichTextEditor>
        </div>
    }
</div>


<MudDialog @ref="_addMemberDialog" Style="width:85vw; max-width:1200px;" OnBackdropClick="HandleBackdropClick">
    <TitleContent>
        <MudText Typo="Typo.h6" Style="font-size:1rem; font-weight:600;">@Localizer["Social History"]</MudText>
        <MudIconButton Icon="@Icons.Material.Filled.Close" Size="Size.Small" OnClick="CancelChanges" Style="margin:-4px; position:absolute; right:16px; top:16px;" />
    </TitleContent>
    <DialogContent>
        <div style="margin:-12px; display:flex; flex-direction:column;">
            <SfGrid @ref="SocialHistoryGrid"
                    TValue="PatientSocialHistory"
                    DataSource="@SocialHistoryList"
                    Toolbar="@(new List<string>() { @Localizer["Add"] })"
                    AllowPaging="true"
                    Style="font-size: 0.85rem; margin-top: 24px;">
                <GridPageSettings PageSize="5"></GridPageSettings>
                <GridEditSettings AllowAdding="true" AllowEditing="true" AllowDeleting="true" AllowEditOnDblClick="true"></GridEditSettings>
                <GridEvents OnActionComplete="ActionCompletedHandler" OnActionBegin="ActionBeginHandler" TValue="PatientSocialHistory"></GridEvents>
                <GridColumns>
                    <GridColumn Field="SocialHistoryId" IsPrimaryKey="true" Visible="false" />
                    <GridColumn Field="CreatedDate" HeaderText="@Localizer["Created Date"]" TextAlign="TextAlign.Center" AllowEditing="false" Format="MM/dd/y" />
                    <GridColumn Field="Occupation" HeaderText="@Localizer["Occupation"]" TextAlign="TextAlign.Center" />
                    <GridColumn Field="LivingSituation" HeaderText="@Localizer["Living Situation"]" TextAlign="TextAlign.Center" />
                    <GridColumn Field="MaritalStatus" HeaderText="@Localizer["Marital Status"]" TextAlign="TextAlign.Center" />
                    <GridColumn Field="LifestyleHabits" HeaderText="@Localizer["Lifestyle Habits"]" TextAlign="TextAlign.Center" />
                    <GridColumn Field="Educationlevel" HeaderText="@Localizer["Education level"]" TextAlign="TextAlign.Center" />
                    <GridColumn HeaderText="@Localizer["Actions"]" TextAlign="TextAlign.Center" Width="75">
                        <GridCommandColumns>
                            <GridCommandColumn Type="CommandButtonType.Delete"
                                               ButtonOption="@(new CommandButtonOptions(){ IconCss = "e-icons e-delete", CssClass = "e-flat" })" />
                        </GridCommandColumns>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
            <div style="display:flex; justify-content:flex-end; gap:12px; padding:16px 24px; border-top:1px solid #E0E0E0;">
                <MudButton Color="Color.Secondary" Variant="Variant.Outlined" OnClick="CancelChanges" Dense="true" Style="min-width:120px; height:40px; font-weight:600;">
                    @Localizer["Cancel"]
                </MudButton>
                <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveChanges" Dense="true" Style="min-width:120px; height:40px; font-weight:600;">
                    @Localizer["Save"]
                </MudButton>
            </div>
        </div>
    </DialogContent>
</MudDialog>


<style>
    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
        }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
    }

</style>