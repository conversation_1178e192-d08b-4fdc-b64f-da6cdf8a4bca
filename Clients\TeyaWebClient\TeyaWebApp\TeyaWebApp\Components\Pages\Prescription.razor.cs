﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;
using Unity;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;


namespace TeyaWebApp.Components.Pages
{
    public partial class Prescription : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject] public IRxNormService RxNormService { get; set; }
        [Inject] public IAssessmentsService AssessmentsService { get; set; }
        [Inject] public IPrescriptionMedicationService PrescriptionMedicationService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IMemberService _MemberService { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        public string drugName { get; set; }
        public string Diagnosis { get; set; }
        public string finalSBD { get; set; }
        private MudDialog _currentmedicdialog;
        public string _value;
        private string _Quantity;
        private string _Frequency;
        private SfRichTextEditor RichTextEditor;
        private Guid PatientID { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        public SfGrid<ActiveMedication> MedicinesGrid { get; set; }
        protected List<string> BrandNames { get; set; } = new List<string>();
        protected List<string> Assessmentsdetails { get; set; } = new List<string>();
        public List<string> BrandSBD { get; set; } = new List<string>();
        private List<ActiveMedication> deleteList { get; set; } = new List<ActiveMedication>();
        private List<ActiveMedication> AddList = new();
        private List<ActiveMedication> medications { get; set; }
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        };

        /// <summary>
        /// get list of all Brand Names from RxNorm and Patient Current Medication from database
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            OrgID = _PatientService.PatientData.OrganizationID;
            activeUserOrganizationId = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(activeUserOrganizationId);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            BrandNames = await RxNormService.GetAllBrandNames();
            Assessmentsdetails = await AssessmentsService.GetDiagnosisListByIdAsync(PatientID, OrgID, Subscription);
            medications = await PrescriptionMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            editorContent = string.Join("<p>",
                medications.OrderByDescending(m => m.StartDate)
                .Select(m => $"Brand Name: {m.BrandName}, Composition: {m.DrugDetails}, Quantity: {m.Quantity}, " +
                             $"Start Date: {(m.StartDate.HasValue ? m.StartDate.Value.ToString("MM-dd-yyyy") : "")}, " +
                             $"End Date: {(m.EndDate.HasValue ? m.EndDate.Value.ToString("MM-dd-yyyy") : "")}"));

        }

        /// <summary>
        /// Update value in Drug Name List
        /// </summary>
        private async Task OnDrugNameChanged(string value)
        {
            drugName = value;

            if (!string.IsNullOrEmpty(value))
            {
                BrandSBD = await RxNormService.GetSBDNamesAsync(value);
                finalSBD = null;
                StateHasChanged();

            }
            else
            {
                BrandSBD.Clear();
                finalSBD = null;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Update value in Diagnosis(assessments)
        /// </summary>
        private async Task OnDiagnosisChanged(string value)
        {
            Diagnosis = value;
            StateHasChanged();
        }

        /// <summary>
        /// search function for assessments
        /// </summary>
        protected Task<IEnumerable<string>> SearchAssessments(string value, CancellationToken cancellationToken)
        {
            IEnumerable<string> result;

            if (cancellationToken.IsCancellationRequested)
            {
                result = Enumerable.Empty<string>();
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                result = Assessmentsdetails.AsEnumerable();
            }
            else
            {
                result = Assessmentsdetails.Where(b => b.Contains(value, StringComparison.OrdinalIgnoreCase)).AsEnumerable();
            }

            return Task.FromResult(result);
        }


        /// <summary>
        /// Search function for auto complete bar 
        /// </summary>
        protected Task<IEnumerable<string>> SearchBrandNames(string value, CancellationToken cancellationToken)
        {
            IEnumerable<string> result;

            if (cancellationToken.IsCancellationRequested)
            {
                result = Enumerable.Empty<string>();
            }
            else if (string.IsNullOrWhiteSpace(value))
            {
                result = BrandNames.AsEnumerable();
            }
            else
            {
                result = BrandNames.Where(b => b.Contains(value, StringComparison.OrdinalIgnoreCase)).AsEnumerable();
            }

            return Task.FromResult(result);
        }

        /// <summary>
        /// Add medication to database & Update to grid,RichTextEditor
        /// </summary>
        private async void AddNewMedication()
        {
            if (string.IsNullOrEmpty(drugName) || string.IsNullOrEmpty(finalSBD) || string.IsNullOrEmpty(Diagnosis))
            {
                Snackbar.Add(@Localizer["Please fill in both Brand Name, Drug Details and Assessments fields"], Severity.Warning);
                return;
            }
            var newMedication = new ActiveMedication
            {
                MedicineId = Guid.NewGuid(),
                PatientId = PatientID,
                PCPId = Guid.Parse(User.id),
                OrganizationId = _PatientService.PatientData.OrganizationID ?? Guid.Empty,
                CreatedBy = Guid.Parse(User.id),
                UpdatedBy = PatientID,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
               // Assessments = Diagnosis,
                BrandName = drugName,
                DrugDetails = finalSBD,
                Quantity = _Quantity ?? "Not Specified",
                Frequency = _Frequency ?? "Not Specified",
                isActive = true,
                StartDate = null,
                EndDate = null,
            };

            AddList.Add(newMedication);

            medications.Add(newMedication);

            await MedicinesGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Reset Fields for closing
        /// </summary>
        private async void ResetInputFields()
        {
            Diagnosis = string.Empty;
            drugName = string.Empty;
            finalSBD = null;
            BrandSBD.Clear();
            _Quantity = null;
            _Frequency = null;
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// To store deleted rows locally in SFgrid 
        /// </summary>
        public void ActionCompletedHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedMedication = args.Data as ActiveMedication;
                var existingItem = AddList.FirstOrDefault(v => v.MedicineId == deletedMedication.MedicineId);

                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.isActive = false;
                    args.Data.UpdatedBy = Guid.Parse(User.id);
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }
        }

        public void ActionBeginHandler(ActionEventArgs<ActiveMedication> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (args.Data.StartDate.HasValue && args.Data.EndDate.HasValue &&
                    args.Data.StartDate > args.Data.EndDate)
                {
                    Snackbar.Add(@Localizer["Start Date cannot be after End Date"], Severity.Warning);

                    // Prevent Grid from exiting Edit Mode
                    args.Cancel = true;
                    return;
                }

                // Proceed with saving if validation passes
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;
            }
        }


        /// <summary>
        /// Changes to be made when clicking on save in dialog
        /// </summary>
        private async Task SaveChanges()
        {
            if (AddList.Count != 0)
            {
                await PrescriptionMedicationService.AddMedicationAsync(AddList, OrgID, Subscription);
            }
            await PrescriptionMedicationService.UpdateMedicationListAsync(deleteList, OrgID, Subscription);
            await PrescriptionMedicationService.UpdateMedicationListAsync(medications, OrgID, Subscription);
            deleteList.Clear();
            AddList.Clear();
            editorContent = string.Join("<p>",
                medications.OrderByDescending(m => m.StartDate)
                .Select(m => $"Brand Name: {m.BrandName}, Composition: {m.DrugDetails}, Quantity: {m.Quantity}, " +
                             $"Start Date: {(m.StartDate.HasValue ? m.StartDate.Value.ToString("MM-dd-yyyy") : "")}, " +
                             $"End Date: {(m.EndDate.HasValue ? m.EndDate.Value.ToString("MM-dd-yyyy") : "")}"));

            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Undo Changes When click on cancel
        /// </summary>
        private async Task CancelChanges()
        {
            deleteList.Clear();
            AddList.Clear();
            medications = await PrescriptionMedicationService.GetMedicationsByIdAsyncAndIsActive(PatientID, OrgID, Subscription);
            ResetInputFields();
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Open Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _currentmedicdialog.ShowAsync();
        }

        /// <summary>
        /// Close dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            ResetInputFields();
            _currentmedicdialog.CloseAsync();
        }

    }
}