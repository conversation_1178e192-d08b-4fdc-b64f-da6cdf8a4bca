﻿@page "/Notes"
@using BusinessLayer.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using System.Text.RegularExpressions
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IRoleService RoleService
@inject HttpClient Http
@using TeyaUIViewModels.ViewModel
@inject ISpeechService speechService
@inject IJSRuntime JSRuntime
@inject ITokenService TokenService
@inject IAddressService AddressService
@inject IInsuranceService InsuranceService
@inject IGuardianService GuardianService
@inject IEmployerService EmployerService
@inject IOrganizationService OrganizationService
@inject IProgressNotesService ProgressNotesService
@using System.Collections.Generic
@using Microsoft.AspNetCore.Components

<MudGrid>

    @if (_PatientService.PatientData != null && !showSingleRecordView)
    {
        <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="() => AddNewRecord()" Class=" ml-7 mt-5 p-2">@Localizer["Add New Record"] </MudButton>
    }

    @if (showSingleRecordView)
    {
        <MudButton Variant="Variant.Outlined" Color="Color.Primary" OnClick="GoBackToEncounters" Class="ml-7 mt-5 p-2">
            @Localizer["Go Back to Encounters"]
        </MudButton>
    }
    @if (PatientCreationFlag == true)
    {
       <MudItem xs="12">
    <MudPaper Elevation="2" Class="my-card mb-4">
        <MudText Typo="Typo.h6" Class="mb-2" Color="Color.Primary">
            <MudIcon Icon="@Icons.Material.Filled.PersonAdd" Class="me-2" />
            @Localizer["Create New Patient"]
        </MudText>

        <!-- Form Fields -->
        <MudGrid Class="align-center" Spacing="3">
            <!-- First Row: First Name, Last Name, DOB -->
            <MudItem xs="12" sm="12" md="4">
                <MudTextField T="string"
                              Label="@Localizer["First Name"]"
                              @bind-Value="member.FirstName"
                              Variant="Variant.Outlined"
                              Validation="ValidateAlphabetic"
                              Margin="Margin.Dense"
                              Immediate="true"
                              Style="background-color: white; border-radius: 8px;" />
            </MudItem>

            <MudItem xs="12" sm="12" md="4">
                <MudTextField T="string"
                              Label="@Localizer["Last Name"]"
                              @bind-Value="member.LastName"
                              Validation="ValidateAlphabetic"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              Immediate="true"
                              Style="background-color: white; border-radius: 8px;" />
            </MudItem>

            <MudItem xs="12" sm="12" md="4">
                <MudDatePicker T="DateTime?"
                               Label="@Localizer["Date Of Birth"]"
                               @bind-Date="member.DateOfBirth"
                               Clearable="true"
                               AutoClose="true"
                               Variant="Variant.Outlined"
                               Margin="Margin.Dense"
                               MaxDate="DateTime.Today"
                               MinDate="@(DateTime.Today.AddYears(-200))"
                               DateFormat="MM/dd/yyyy"
                               PickerVariant="PickerVariant.Inline"
                               PopoverDirection="Direction.Top"
                               Class="custom-datepicker compact-picker"
                               Style="background-color: white; border-radius: 8px;" />
            </MudItem>

            <!-- Second Row: Email, Mobile, SSN -->
            <MudItem xs="12" sm="12" md="4">
                <MudTextField T="string"
                              Label="@Localizer["Email"]"
                              Validation="ValidateEmail"
                              @bind-Value="member.Email"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              Immediate="true"
                              Style="background-color: white; border-radius: 8px;" />
            </MudItem>

            <MudItem xs="12" sm="12" md="4">
                <MudTextField T="string"
                              Label="@Localizer["Mobile Number"]"
                              @bind-Value="member.PhoneNumber"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              Validation="ValidatePhoneNumber"
                              Placeholder="(*************"                              
                              Immediate="true"
                              Style="background-color: white; border-radius: 8px;" />
            </MudItem>

            <MudItem xs="12" sm="12" md="4">
                <MudTextField T="string"
                              Label="@Localizer["SSN"]"
                              @bind-Value="member.SSN"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense"
                              Validation="ValidateSSN"
                              MaskPlaceholder="_"
                              Placeholder="***********"
                              Immediate="true"
                              Style="background-color: white; border-radius: 8px;" />
            </MudItem>

            <!-- Final Row: Add Button aligned right -->
            
            <MudItem xs="12" Class="d-flex justify-end mt-4">
                <MudButton Color="Color.Primary"
                           Variant="Variant.Filled"
                           Class="uniform-button me-2"
                           OnClick="@(async () => await CreatePatient())">
                    @Localizer["Add"]
                </MudButton>
            </MudItem>
        </MudGrid>
    </MudPaper>
</MudItem>

    }
    @if (isDataLoading)
    {
        <MudItem xs="12" Class="text-center mt-4">
            <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
            <MudText Typo="Typo.subtitle2" Class="mt-2">Loading...</MudText>
        </MudItem>
    }
    else
    {
        @if (record != null)
        {
            var NotesData = ExtractNotesData(record.Notes);
            <MudItem xs="12">
                <MudCard Elevation="4" Class="mb-1 my-card">

                    <MudCardHeader Class="d-flex justify-space-between align-center position-relative">


                        <div class="d-flex align-center justify-start" style="width: 100%;">
                            <div class="patient-profile-top-left d-flex flex-column align-start px-2 py-1">
                                <MudText Typo="Typo.h6" Color="Color.Primary" Style="font-size: 1.25rem;">
                                    @record.PatientName
                                </MudText>

                                <MudText Typo="Typo.body2" Color="Color.Secondary" Class="record-date">
                                    Record Date: @record.DateTime.ToString("g")
                                </MudText>
                            </div>
                        </div>
                        @if (productVisibility)
                        {

                            <div class="button-container">
                                @if (record.isEditable == true && record.Transcription == String.Empty)
                                {
                                    <MudButton Variant="Variant.Filled"
                                               OnClick="() => ShowMicrophone(record)"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.Mic"
                                               Size="Size.Large"
                                               Class="step-button next-button">
                                        @Localizer["Teya AI"]
                                    </MudButton>
                                }
                            </div>
                        }



                    </MudCardHeader>

                    <!-- Replace the existing div structure in your MudCardContent with this: -->

<MudCardContent>
    <div class="d-flex flex-nowrap notes-transcription-container">
        <!-- First Column: Notes (Dynamic Height, No Scroll) -->
        <div class="notes-column flex-fill me-2">
            <div class="notes-content">
                @foreach (var section in NotesData)
                {
                    @foreach (var kvp in section)
                    {
                        <MudCard Class="mt-3 pa-4">
                            <p class="section-heading">
                                @kvp.Key
                            </p>
                            @foreach (var data in kvp.Value)
                            {
                                <p class="subsection-heading" style="font-size: 1rem; color: #333; font-weight: 500; margin-top: 12px;">
                                    @data.Key
                                </p>
                                @if (ListDetails.Any(d => d.Name.Replace(" ", "").Equals(data.Key.Replace(" ", ""), StringComparison.OrdinalIgnoreCase)))
                                {
                                    var matchedItem = ListDetails.FirstOrDefault(d => d.Name.Replace(" ", "").Equals(data.Key.Replace(" ", ""), StringComparison.OrdinalIgnoreCase));
                                    var matchedString = matchedItem?.Name;
                                    var recordNotes = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes);
                                    var TotalText = String.Empty;
                                    if (recordNotes != null &&
                                    recordNotes.ContainsKey(kvp.Key) &&
                                    recordNotes[kvp.Key].ContainsKey(data.Key))
                                    {
                                        var fullContent = recordNotes[kvp.Key][data.Key];
                                        TotalText = fullContent;
                                        if (!string.IsNullOrEmpty(fullContent))
                                        {
                                            // For the second format, we need to extract the editable user content
                                            // which is everything before the <hr> tag
                                            var hrPattern = @"<hr[^>]*>";
                                            var hrMatch = Regex.Match(fullContent, hrPattern);
                                            if (hrMatch.Success)
                                            {
                                                // Extract everything before the <hr> tag
                                                data_value = fullContent.Substring(0, hrMatch.Index).Trim();
                                                // Remove the surrounding <div contenteditable='true'> tags if present
                                                var divPattern = @"<div[^>]contenteditable\s=\s*['""]true['""][^>]>(.?)<\/div>";
                                                var divMatch = Regex.Match(data_value, divPattern, RegexOptions.Singleline);
                                                if (divMatch.Success)
                                                {
                                                    data_value = divMatch.Groups[1].Value.Trim();
                                                }
                                            }
                                            else
                                            {
                                                // No <hr> tag found, treat entire content as user content
                                                data_value = fullContent.Trim();
                                                // Still try to remove contenteditable div if present
                                                var divPattern = @"<div[^>]contenteditable\s=\s*['""]true['""][^>]>(.?)<\/div>";
                                                var divMatch = Regex.Match(data_value, divPattern, RegexOptions.Singleline);
                                                if (divMatch.Success)
                                                {
                                                    data_value = divMatch.Groups[1].Value.Trim();
                                                }
                                            }
                                        }
                                        else
                                        {
                                            data_value = String.Empty;
                                        }
                                    }
                                    var parameters = new Dictionary<string, object>
                    {
                    { "PatientID", record.PatientId },
                    { "Data", data_value },
                    { "OrgId",record.OrganizationId },
                    {"TotalText",TotalText},
                    { "OnValueChanged", EventCallback.Factory.Create<string>(this, async (updatedValue) =>
                    {
                    // Update the specific field in the record
                    var notesDict = JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(record.Notes)
                    ?? new Dictionary<string, Dictionary<string, string>>();
                    if (!notesDict.ContainsKey(kvp.Key))
                    notesDict[kvp.Key] = new Dictionary<string, string>();
                    notesDict[kvp.Key][data.Key] = updatedValue;
                    TotalText=updatedValue;
                    record.Notes = JsonSerializer.Serialize(notesDict);
                    await InvokeAsync(StateHasChanged);
                    })
                    }};
                                    var componentType = GetComponentType(matchedString.Replace(" ", ""));
                                    if (componentType != null)
                                    {
                                        if (!record.isEditable)
                                        {
                                            var selectedText = CleanHtml(TotalText);
                                            <div>@((MarkupString)selectedText)</div>
                                        }
                                        else
                                        {
                                            <DynamicComponent Type="componentType" Parameters="parameters" @key="@($"{componentRefreshKey}{record.Id}{kvp.Key}_{data.Key}")" />
                                        }
                                    }
                                    else
                                    {
                                        if (!record.isEditable)
                                        {
                                            var selectedText = CleanHtml(TotalText);
                                            <div>@((MarkupString)selectedText)</div>
                                        }
                                        else
                                        {
                                            var editorKey = $"{record.Id}{kvp.Key}{data.Key}";
                                            // Initialize if not exists
                                            if (!isEditingDict.ContainsKey(editorKey))
                                            {
                                                isEditingDict[editorKey] = false;
                                            }
                                            var content = GetEditorContent(record, kvp.Key, data.Key);
                                            editorContents[editorKey] = string.IsNullOrEmpty(content) ? "<div>Click to add notes...</div>" : content;
                                            <div class="description-container">
                                                @if (!isEditingDict[editorKey])
                                                {
                                                    <div class="description-box @(string.IsNullOrEmpty(editorContents[editorKey]) ? "empty" : "")"
                                                         @onclick="@(() => OpenEdit(editorKey))">
                                                        <div class="description-content">
                                                            @((MarkupString)CleanHtml(editorContents[editorKey]))
                                                        </div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="editor-container">
                                                        <SfRichTextEditor Value="@editorContents[editorKey]" SaveInterval="saveInterval"
                                                                          ValueChanged="@((string newValue) => HandleRichTextChange(record, kvp.Key, data.Key,newValue))">
                                                            <RichTextEditorToolbarSettings Items="@Tools">
                                                                <RichTextEditorCustomToolbarItems>
                                                                    <RichTextEditorCustomToolbarItem Name="close">
                                                                        <Template>
                                                                            <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                                                           Size="Size.Small"
                                                                                           OnClick="@(() => CloseRTE(editorKey))" />
                                                                        </Template>
                                                                    </RichTextEditorCustomToolbarItem>
                                                                </RichTextEditorCustomToolbarItems>
                                                            </RichTextEditorToolbarSettings>
                                                        </SfRichTextEditor>
                                                    </div>
                                                }
                                            </div>
                                        }
                                    }
                                }
                                else
                                {
                                    if (!record.isEditable)
                                    {
                                        var textData = GetEditorContent(record, kvp.Key, data.Key);
                                        var selectedText = CleanHtml(textData);
                                        <div>@((MarkupString)selectedText)</div>
                                    }
                                    else
                                    {
                                        var editorKey = $"{record.Id}{kvp.Key}{data.Key}";
                                        // Initialize if not exists
                                        if (!isEditingDict.ContainsKey(editorKey))
                                        {
                                            isEditingDict[editorKey] = false;
                                        }
                                        var content = GetEditorContent(record, kvp.Key, data.Key);
                                        editorContents[editorKey] = string.IsNullOrEmpty(content) ? "<div>Click to add notes...</div>" : content;
                                        <div class="description-container">
                                            @if (!isEditingDict[editorKey])
                                            {
                                                <div class="description-box @(string.IsNullOrEmpty(editorContents[editorKey]) ? "empty" : "")"
                                                     @onclick="@(() => OpenEdit(editorKey))">
                                                    <div class="description-content">
                                                        @((MarkupString)CleanHtml(editorContents[editorKey]))
                                                    </div>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="editor-container">
                                                    <SfRichTextEditor Value="@editorContents[editorKey]" SaveInterval="saveInterval"
                                                                      ValueChanged="@((string newValue) => HandleRichTextChange(record, kvp.Key, data.Key,newValue))">
                                                        <RichTextEditorToolbarSettings Items="@Tools">
                                                            <RichTextEditorCustomToolbarItems>
                                                                <RichTextEditorCustomToolbarItem Name="close">
                                                                    <Template>
                                                                        <MudIconButton Icon="@Icons.Material.Filled.Close"
                                                                                       Size="Size.Small"
                                                                                       OnClick="@(() => CloseRTE(editorKey))" />
                                                                    </Template>
                                                                </RichTextEditorCustomToolbarItem>
                                                            </RichTextEditorCustomToolbarItems>
                                                        </RichTextEditorToolbarSettings>
                                                    </SfRichTextEditor>
                                                </div>
                                            }
                                        </div>
                                    }
                                }
                            }
                        </MudCard>
                    }
                }
            </div>
        </div>
        
        @if (record.Transcription != String.Empty)
        {
            <!-- Second Column: Transcription (Fixed Width, Scrollable Content) -->
            <div class="transcription-column ms-4">
                @if (record.Transcription != String.Empty)
                {
                    <audio controls Class="custom-audio" style="margin-bottom: 12px;">
                        <source src="@GetAudioUrl(record.Id)" type="audio/mp3" />
                        Your browser does not support the audio tag.
                    </audio>
                }
                <MudText Class="transcription-heading">
                    @Localizer["Transcription"]
                </MudText>
                
                <!-- Transcription content container that matches notes height -->
                <MudPaper Class="pa-4 transcription-content-wrapper">
                    <div class="conversation-container">
                        @if (!string.IsNullOrEmpty(record.Transcription))
                        {
                            var lines = record.Transcription.Split('\n');
                            string currentSpeaker = "";
                            Dictionary<string, string> speakerColors = new Dictionary<string, string>();
                            Dictionary<string, int> speakerPositions = new Dictionary<string, int>();
                            int speakerCount = 0;
                            @foreach (var line in lines)
                            {
                                if (string.IsNullOrWhiteSpace(line))
                                {
                                    continue;
                                }
                                // Check if line starts with speaker identifier
                                var parts = line.Split(new[] { ":" }, 2, StringSplitOptions.None);
                                if (parts.Length == 2)
                                {
                                    var speaker = parts[0].Trim();
                                    var message = parts[1].Trim();
                                    if (speaker.Equals("Unknown", StringComparison.OrdinalIgnoreCase))
                                    {
                                        continue;
                                    }
                                    currentSpeaker = speaker;
                                    // Assign color and position if this is a new speaker
                                    if (!speakerColors.ContainsKey(speaker))
                                    {
                                        speakerColors[speaker] = GetSpeakerColor(speakerCount);
                                        speakerPositions[speaker] = speakerCount % 2; // 0 = left, 1 = right
                                        speakerCount++;
                                    }
                                    var alignment = speakerPositions[speaker] == 0 ? "left-align" : "right-align";
                                    string speakerColor = speakerColors[speaker];
                                    string bubbleColor = GetBubbleColor(speakerColor);
                                    <div class="conversation-message @alignment">
                                        <div class="speaker-name-container" style=@($"color: {speakerColor}")>
                                            <MudText Typo="Typo.subtitle2" Class="speaker-name">@speaker</MudText>
                                        </div>
                                        <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                            <MudText Style="text-align: left;">@message</MudText>
                                        </MudPaper>
                                    </div>
                                }
                                else
                                {
                                    // If no speaker identified, use the current speaker's alignment
                                    if (!string.IsNullOrEmpty(currentSpeaker) && speakerPositions.ContainsKey(currentSpeaker))
                                    {
                                        var alignment = speakerPositions[currentSpeaker] == 0 ? "left-align" : "right-align";
                                        string bubbleColor = GetBubbleColor(speakerColors[currentSpeaker]);
                                        <div class="conversation-message @alignment">
                                            <MudPaper Elevation="0" Class="message-bubble" Style=@($"background-color: {bubbleColor}")>
                                                <MudText Style="text-align: left;">@line</MudText>
                                            </MudPaper>
                                        </div>
                                    }
                                    else
                                    {
                                        // Fallback for lines without a speaker context
                                        <div class="conversation-message left-align">
                                            <MudPaper Elevation="0" Class="message-bubble" Style="background-color: #f5f5f5">
                                                <MudText Style="text-align: left;">@line</MudText>
                                            </MudPaper>
                                        </div>
                                    }
                                }
                            }
                        }
                    </div>
                </MudPaper>
            </div>
        }
    </div>
</MudCardContent>
                    @if (record.isEditable == true)
                    {
                        <div class="d-flex justify-end mt-4 mb-2 me-6">
                            <MudButton Color="Color.Primary" Variant="Variant.Filled" Class="uniform-button me-2" OnClick="@(async () => await SaveRecord(record))">
                                <MudIcon Icon="@Icons.Material.Filled.Save" Class="mr-1" />
                                @Localizer["Save"]
                            </MudButton>
                            <MudButton Color="Color.Primary" Variant="Variant.Outlined" Class="uniform-button me-2" OnClick="@(async () => await LockRecord(record))">
                                <MudIcon Icon="@Icons.Material.Filled.Lock" Class="mr-1" />
                                @Localizer["Lock"]
                            </MudButton>
                        </div>
                    }
                </MudCard>
            </MudItem>
        }
        else
        {
            <MudItem xs="12" Class="text-center mt-4 mb-10">
                <MudText Typo="Typo.h6" Color="Color.Primary">
                    @Localizer["No record found."]
                </MudText>
            </MudItem>
        }
    }
</MudGrid>
<MudDialog Class="recording-dialog" @ref="MicrophoneDialog" OnBackdropClick="HandleBackdropClick"
           Style="width: 500px; border-radius: 12px; overflow: hidden; background: white;">
    <DialogContent>
        <div class="dialog-header">
            <MudIconButton Icon="@Icons.Material.Filled.Close" Color="Color.Error" Size="Size.Small" OnClick="CloseMicrophoneDialog" />
        </div>
        <div class="recording-container">
            @if (isLoading)
            {
                <!-- Processing state -->
                <div class="processing-indicator">
                    <span class="material-icons spin">@Localizer["autorenew"]</span>
                    <div class="processing-text">@Localizer["Processing..."]</div>
                </div>
            }
            else if (isRecorderActive)
            {
                <!-- Recording state -->
                <div class="recording-indicator-start" style="@(isPaused ? "background-color: #2bcbba;" : "")">
                    @(isPaused ? "Paused" : "Recording...")
                </div>
                <div class="wave-container">
                    <div class="wave-group">
                        @for (int i = 0; i < 40; i++)
                        {
                            <div class="wave-bar" style="@GetBarStyle(i)"></div>
                        }
                    </div>
                </div>
                <div>
                    <span class="timer-recording">@FormatCallDuration(callDuration)</span>
                </div>
                <div class="controls-wrapper">
                    <MudButton StartIcon="@(isPaused ? Icons.Material.Filled.PlayArrow : Icons.Material.Filled.Pause)"
                               OnClick="OnPauseIconClick"
                               Class="end-visit-btn">
                        @Localizer[isPaused ? "Resume" : "Pause"]
                    </MudButton>

                    <MudButton Class="end-visit-btn"
                               OnClick="OnMicIconClick"
                               StartIcon="@Icons.Material.Filled.StopCircle">
                        @Localizer["Stop"]
                    </MudButton>
                </div>
            }
            else
            {
                <!-- Initial state -->
                <div class="recording-indicator" style="background-color: red;">
                    @Localizer["Teya Ai Scribe"]
                </div>
                <div class="wave-container">
                    <div class="wave-group">
                        @for (int i = 0; i < 40; i++)
                        {
                            <div class="wave-bar" style="@GetNormalBarStyle()"></div>
                        }
                    </div>
                </div>
                <div>
                    <span class="timer">@FormatCallDuration(callDuration)</span>
                </div>
                <div class="controls-wrapper">
                    <MudButton Class="start-visit-btn"
                               OnClick="OnMicIconClick"
                               StartIcon="@Icons.Material.Filled.Mic">
                        @Localizer["Start"]
                    </MudButton>
                </div>
            }
        </div>
    </DialogContent>
</MudDialog>

<style>
    /* General Styles */
    body {
        margin: 0;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f4f4f4;
    }

    .description-container {
        margin-bottom: 16px;
    }

    .description-box {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

        .description-box:hover {
            border-color: #999;
            background-color: #f5f5f5;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .description-box.empty {
            color: #888;
            font-style: italic;
            min-height: 24px;
            line-height: 24px;
        }

    .description-content {
        min-height: 24px;
    }

    .editor-container {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        transition: all 0.2s ease;
    }

        .editor-container:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

    /* Syncfusion RTE specific overrides */
    .e-richtexteditor {
        border: none !important;
        border-radius: 0 0 4px 4px !important;
    }

    .e-rte-toolbar {
        background-color: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
        padding: 4px !important;
    }

    .e-toolbar-items {
        background: transparent !important;
    }

    .e-rte-content {
        padding: 8px 12px !important;
        min-height: 100px !important;
    }

    .dialog-header {
        display: flex;
        justify-content: flex-end;
    }

    .full-width {
        width: 100%;
    }

    .align-center {
        display: flex;
        align-items: center;
    }

    /* Responsive Styles */
    @@media (max-width: 600px) {
        .pr-sm-4 {
            padding-right: 0 !important;
        }
    }

    /* Card Styles */
    .my-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        background-color: rgba(var(--mud-palette-primary-rgb), 0.06);
        border-radius: 12px;
    }

        .my-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

    .first-column {
        display: flex;
        flex-direction: column;
    }

    .second-column {
        width: 40%; /* Adjusted width for better layout */
        min-width: 300px;
    }

    .mud-card-content {
        padding: 16px;
    }

    


    .conversation-message {
        display: flex;
        flex-direction: column;
        max-width: 80%;
        margin-bottom: 8px;
    }

    .left-align {
        align-self: flex-start;
    }

    .right-align {
        align-self: flex-end;
    }

        .right-align .speaker-name-container {
            align-self: flex-end;
        }

        .right-align .message-bubble {
            margin-left: auto;
        }

    .speaker-name {
        margin-bottom: 4px;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .message-bubble {
        padding: 8px 12px;
        border-radius: 12px;
        word-break: break-word;
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .left-align .message-bubble {
        border-top-left-radius: 2px;
    }

    .right-align .message-bubble {
        border-top-right-radius: 2px;
    }

    .custom-rte-container {
        position: relative;
        transition: margin-top 0.3s ease;
    }

        .custom-rte-container .e-rte-toolbar {
            display: none;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            border-radius: 4px;
            margin-bottom: 0;
        }

        .custom-rte-container:hover .e-rte-toolbar {
            display: block;
        }

        .custom-rte-container .e-rte-toolbar-wrapper {
            background-color: white;
            border-radius: 4px;
        }

    .button-container {
        display: flex;
        gap: 8px;
        padding: 4px;
        margin: 4px;
        width: fit-content;
        z-index: 5; /* Ensure it doesn't overlap with patient profile */
    }

    .action-button {
        min-width: 120px;
        height: 36px;
        margin: 0 !important;
        border-radius: var(--mud-default-borderradius);
        transition: all 0.2s ease;
        font-weight: 500;
    }

        .action-button:hover {
            transform: translateY(-1px);
            box-shadow: var(--mud-elevation-1);
        }

    .uniform-button {
        min-width: 95px; /* Adjust as needed */
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Profile and Audio Component Margin Adjustments */
    .patient-info-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 5px;
        margin: 0; /* Reduced margin */
    }

    

    .patient-name {
        font-weight: 600;
        padding-bottom: 4px;
    }

    .record-date {
        font-style: italic;
        font-size: 0.85rem;
    }

    /* Transition for Notes Page Loading */
    .notes-page {
        animation: slideIn 0.5s ease-out forwards;
        opacity: 0;
    }

    @@keyframes slideIn {
        from {
            transform: translateY(100%);
            opacity: 0;
        }

        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    /* Custom styles for section headings */
    .section-heading {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(0,0,0,0.1);
        color: var(--mud-palette-primary);
    }


    .subsection-heading {
        margin-top: 16px;
        margin-bottom: 8px;
        font-weight: 500;
    }

    /* Custom styles for patient information */
    .patient-info {
        padding: 12px;
        background-color: rgba(255,255,255,0.8);
        border-radius: 8px;
        margin-bottom: 16px;
    }

    /* Custom styles for buttons */
    .uniform-button {
        font-weight: 500;
        padding: 0 16px;
    }

    /* Custom styles for form elements */
    .form-element {
        margin-bottom: 16px;
    }

    /* Custom styles for loading indicator */
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
    }

    /* Custom styles for empty states */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px;
        color: #666;
    }

    .empty-state-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }

    /* Custom styles for buttons in dialog */
    .dialog-button {
        margin: 0 8px;
    }

    /* Custom styles for the recording dialog */
    .recording-dialog {
        border-radius: 12px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    }

    .recording-container {
        padding: 16px;
    }

    .processing-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .processing-text {
        margin-top: 8px;
        font-weight: 500;
    }

    .recording-indicator-start {
        padding: 8px 16px;
        border-radius: 16px;
        color: white;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .recording-indicator {
        padding: 8px 16px;
        border-radius: 16px;
        color: white;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .wave-container {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 16px 0;
    }

    .wave-group {
        display: flex;
        height: 100%;
        align-items: center;
    }

    .wave-bar {
        width: 4px;
        background-color: #ddd;
        margin: 0 2px;
        border-radius: 2px;
    }

    .controls-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 16px;
    }

    .end-visit-btn {
        min-width: 120px;
        height: 36px;
        margin: 0 8px;
    }

    .timer-recording, .timer {
        font-family: monospace;
        font-size: 1.2rem;
        font-weight: 600;
        color: #333;
        margin: 8px 0;
    }

    /* Custom styles for the microphone dialog */
    .microphone-dialog {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .microphone-icon {
        font-size: 48px;
        margin-bottom: 16px;
    }

    /* Custom styles for the section cards */
    .section-card {
        transition: all 0.3s ease;
    }

        .section-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

    /* Custom styles for the rich text editor */
    .rte-container {
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
    }

    .rte-toolbar {
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
        padding: 8px;
    }

    .rte-content {
        padding: 12px;
        min-height: 100px;
    }

    /* Custom styles for the patient creation form */
    .patient-form {
        padding: 24px;
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }

    .form-title {
        margin-bottom: 24px;
        color: #1976d2;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 24px;
    }

        .form-actions button {
            margin-left: 12px;
        }

    /* Custom styles for the patient info card */
    .patient-card {
        background-color: white;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .patient-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
    }

    .patient-card-title {
        font-weight: 600;
        margin-left: 12px;
    }

    .patient-card-content {
        display: flex;
        flex-direction: column;
    }

    .patient-card-row {
        display: flex;
        margin-bottom: 8px;
    }

    .patient-card-label {
        font-weight: 500;
        width: 120px;
    }

    .patient-card-value {
        flex: 1;
    }

    /* Custom styles for the patient info dialog */
    .patient-info-dialog {
        min-width: 300px;
    }

        .patient-info-dialog .dialog-title {
            font-weight: 600;
            margin-bottom: 16px;
        }

        .patient-info-dialog .dialog-content {
            padding: 0 16px 16px 16px;
        }

    .patient-info-dialog {
        min-width: 300px;
    }

        .patient-info-dialog .dialog-title {
            font-weight: 600;
            margin-bottom: 16px;
        }

        .patient-info-dialog .dialog-content {
            padding: 0 16px 16px 16px;
        }

    .patient-info-dialog {
        min-width: 300px;
    }

        .patient-info-dialog .dialog-title {
            font-weight: 600;
            margin-bottom: 16px;
        }

        .patient-info-dialog .dialog-content {
            padding: 0 16px 16px 16px;
        }

    .step-button {
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        text-transform: none;
        transition: all 0.3s ease;
        min-width: 140px;
    }

    .next-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    .patient-profile-top-left {
        text-align: left;
    }

    .transcription-heading {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 12px;
        padding-bottom: 8px;
        color: var(--mud-palette-primary);
    }

    .custom-audio {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.15); /* medium background */
        border: 1px solid rgba(var(--mud-palette-primary-rgb), 0.4); /* subtle border */
        border-radius: 2rem;
    }

    .mud-popover .mud-picker-inline {
        max-height: 250px;
        overflow-y: auto;
    }

    <!-- nw css-->
    /* Updated container styles for dynamic height layout */
    .notes-transcription-container {
        display: flex;
        min-height: fit-content; /* Allow container to grow with content */
        align-items: flex-start; /* Align items to the top */
    }

    /* Notes column - dynamic height, no scroll */
    .notes-column {
        flex: 1;
        min-width: 0; /* Prevent flex item from overflowing */
    }

    .notes-content {
        /* Remove any height restrictions and overflow settings */
        height: auto;
        overflow: visible;
    }

    /* Transcription column - fixed width, matches notes height */
    .transcription-column {
        width: 40%;
        min-width: 300px;
        display: flex;
        flex-direction: column;
    }

    .transcription-content-wrapper {
        flex: 1;
        display: flex;
        flex-direction: column;
        /* This will make the transcription match the height of the notes column */
        min-height: 400px; /* Minimum height fallback */
    }

    .conversation-container {
        flex: 1;
        overflow-y: auto; /* Only this container scrolls */
        overflow-x: hidden;
        display: flex;
        flex-direction: column;
        gap: 6px;
        padding: 10px;
        border-radius: 8px;
        background-color: #f8f9fa;
        /* Remove max-height to allow natural sizing */
    }

    /* Update existing styles to remove fixed heights and scrolling from notes */
    .first-column {
        display: flex;
        flex-direction: column;
        height: auto; /* Changed from fixed height */
        overflow: visible; /* Changed from hidden */
    }

    .second-column {
        width: 40%;
        min-width: 300px;
        height: auto; /* Let it size naturally */
        display: flex;
        flex-direction: column;
    }

    /* Ensure transcription header stays fixed */
    .transcription-heading {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 12px;
        padding-bottom: 8px;
        color: var(--mud-palette-primary);
        flex-shrink: 0; /* Prevent header from shrinking */
    }

    /* Custom audio player stays fixed at top */
    .custom-audio {
        background-color: rgba(var(--mud-palette-primary-rgb), 0.15);
        border: 1px solid rgba(var(--mud-palette-primary-rgb), 0.4);
        border-radius: 2rem;
        flex-shrink: 0; /* Prevent audio player from shrinking */
    }

    /* Remove old conflicting styles */
    .mud-card-content {
        padding: 16px;
        /* Remove any height restrictions */
    }

    /* Responsive adjustments */
    @@media (max-width: 1200px) {
        .transcription-column

    {
        width: 35%;
        min-width: 280px;
    }

    }

    @@media (max-width: 900px) {
        .notes-transcription-container

    {
        flex-direction: column;
    }

    .transcription-column {
        width: 100%;
        margin-top: 20px;
        margin-left: 0 !important;
    }

    .transcription-content-wrapper {
        min-height: 300px; /* Smaller minimum height on mobile */
    }

    }
</style>