﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIModels.Model;

namespace TeyaWebApp.Components.Pages
{
    public partial class Providers :ComponentBase
    {
        private MudForm? form;
        private UpToDate FormData = new UpToDate();
        private string? _selectedOrganization;
        private bool isUpToDateTabDisabled = true;
        private int activeTabIndex = 0;

        private async void HandleValidSubmit()
        {
            try
            {
                FormData.Id = Guid.NewGuid();

                if (!string.IsNullOrEmpty(_selectedOrganization))
                {
                    var organizations = await OrganizationService.GetAllOrganizationsAsync();
                    var selectedOrg = organizations.FirstOrDefault(org => org.OrganizationName == _selectedOrganization);

                    if (selectedOrg != null)
                    {
                        FormData.OrganizationId = selectedOrg.OrganizationId;
                    }
                    else
                    {
                        Logger.LogWarning("@Localizer['OrganizationNotFound']");
                    }
                }

                var isSaved = await UpToDateService.AddUpToDateAsync(FormData);

                if (isSaved)
                {
                    Logger.LogInformation("@Localizer['FormSavedSuccess']");
                    isUpToDateTabDisabled = false;
                    activeTabIndex = 1;
                    FormData = new UpToDate();
                    StateHasChanged();
                }
                else
                {
                    Logger.LogError("@Localizer['FormSaveFailure']");
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "@Localizer['FormSubmissionError']");
            }
        }

        private async Task<IEnumerable<string>> SearchOrganizations(string searchTerm, CancellationToken cancellationToken)
        {
            try
            {
                var result = new List<string>();
                var organizations = await OrganizationService.GetAllOrganizationsAsync();

                if (!string.IsNullOrWhiteSpace(searchTerm))
                {
                    result = organizations
                        .Where(org => org.OrganizationName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .Select(org => org.OrganizationName)
                        .ToList();
                }
                else
                {
                    result = organizations.Select(org => org.OrganizationName).ToList();
                }

                return result;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "@Localizer['FetchOrganizationsError']");
                return new List<string>();
            }
        }
    }
}
