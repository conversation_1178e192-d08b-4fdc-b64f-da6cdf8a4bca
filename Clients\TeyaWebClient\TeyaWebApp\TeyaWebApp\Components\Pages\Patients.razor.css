﻿
@@font -face {
    font-family: 'Patient-icons';
    src: url(data:application/x-font-ttf;charset=utf-8;base64,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) format('truetype');
    font-weight: normal;
    font-style: normal;
}

[class^="sf-icon-"],
[class*=" sf-icon-"] {
    font-family: 'Patient-icons' !important;
    speak: none;
    font-size: 55px;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: inherit;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.sf-icon-personal-info:before {
    content: "\e700";
}

.sf-icon-guardian-info:before {
    content: "\e701";
}

.sf-icon-employer-info:before {
    content: "\e702";
}

.sf-icon-demographics-info:before {
    content: "\e703";
}

.sf-icon-insurance-info:before {
    content: "\e704";
}

.sf-icon-address-info:before {
    content: "\e705";
}

.sf-icon-misc-info:before {
    content: "\e706";
}

/* ===== MODERN PATIENT MANAGEMENT STYLES ===== */

/* Container and Layout */
.patient-management-container {
    min-height: 100vh;
    padding: 0;
}

    /* Improved container spacing for wider layout */
    .patient-management-container .mud-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }



/* Stepper Styling */
.patient-stepper-section {
    width: 90%;
    margin: 40px auto;
}

#patient-step-content {
    position: relative;
    width: 88%;
    min-height: 250px;
    margin: 0 auto;
}

.step-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    pointer-events: none;
}

    /* Display the active step content */
    .step-content.step-active {
        animation: fadeInUp 0.4s;
        opacity: 1;
        z-index: 1000;
        pointer-events: all;
    }

/* Keyframes */
@@keyframes fadeInUp {
    from {
        opacity: 0;
        -webkit-transform: translate3d(0, 50%, 0);
        transform: translate3d(0, 50%, 0);
    }

    to {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
    }
}

/* Card Styling */
.member-details-card,
.guardian-details-card,
.employer-details-card,
.stats-details-card,
.insurance-details-card,
.address-details-card,
.miscellaneous-details-card,
.action-buttons-card {
    border-radius: 16px;
    border: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

/* Card Headers */
.card-header-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-icon {
    color: #667eea;
    font-size: 1.5rem;
}

.header-title {
    font-weight: 600;
    margin: 0;
}

/* Card Content */
.card-content-padding {
    padding: 2rem;
}

/* Form Elements */
.modern-textfield,
.modern-autocomplete,
.modern-datepicker {
    margin-bottom: 0.5rem;
}

    .modern-textfield ::deep .mud-input-root,
    .modern-autocomplete ::deep .mud-input-root,
    .modern-datepicker ::deep .mud-input-root {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

        .modern-textfield ::deep .mud-input-root:hover,
        .modern-autocomplete ::deep .mud-input-root:hover,
        .modern-datepicker ::deep .mud-input-root:hover {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .modern-textfield ::deep .mud-input-root.mud-input-root-focused,
        .modern-autocomplete ::deep .mud-input-root.mud-input-root-focused,
        .modern-datepicker ::deep .mud-input-root.mud-input-root-focused {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
        }

    /* Date Picker Improvements */
    .modern-datepicker ::deep .mud-picker-input-button {
        border-radius: 0 12px 12px 0;
    }

    .modern-datepicker ::deep .mud-picker-popup {
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        max-width: 350px;
        transform: scale(0.9);
        transform-origin: top left;
    }

    .modern-datepicker ::deep .mud-picker-calendar-container {
        max-width: 320px;
    }

    .modern-datepicker ::deep .mud-picker-calendar-header {
        padding: 8px 16px;
    }

    .modern-datepicker ::deep .mud-picker-calendar-content {
        padding: 8px;
    }

    .modern-datepicker ::deep .mud-button-month {
        font-size: 0.875rem;
        padding: 4px 8px;
    }

    .modern-datepicker ::deep .mud-picker-calendar-day {
        width: 32px;
        height: 32px;
        font-size: 0.875rem;
    }

/* Step Navigation Styling */
.step-navigation {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.step-button {
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: none;
    transition: all 0.3s ease;
    min-width: 140px;
}

.next-button, .submit-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

    .next-button:hover, .submit-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

.previous-button {
    border: 2px solid #6b7280;
    color: #6b7280;
    background: rgba(107, 114, 128, 0.05);
}

    .previous-button:hover {
        background: rgba(107, 114, 128, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(107, 114, 128, 0.2);
    }

.delete-button {
    border: 2px solid #ef4444;
    color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

    .delete-button:hover {
        background: rgba(239, 68, 68, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
    }

/* Section Titles */
.section-title {
    color: #2c3e50;
    font-weight: 600;
    display: flex;
    align-items: center;
}

/* Photo Upload Section */
.photo-upload-section {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.upload-dropzone {
    border: 2px dashed #667eea;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    background: rgba(102, 126, 234, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

    .upload-dropzone:hover {
        border-color: #5a67d8;
        background: rgba(102, 126, 234, 0.1);
        transform: translateY(-2px);
    }

.upload-icon {
    color: #667eea;
    font-size: 3rem;
}

.uploaded-filename {
    color: #667eea;
    font-weight: 500;
}

.uploaded-photo-section {
    background: rgba(34, 197, 94, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.photo-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.success-chip {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    color: white;
    border-radius: 20px;
    font-weight: 500;
}

.remove-photo-btn {
    border-radius: 8px;
    transition: all 0.2s ease;
}

    .remove-photo-btn:hover {
        background-color: rgba(239, 68, 68, 0.1);
        transform: scale(1.1);
    }

.preview-btn {
    color: #667eea;
    font-weight: 500;
}

.image-preview {
    text-align: center;
}

.preview-image {
    max-width: 300px;
    max-height: 300px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Action Buttons */
.action-buttons-section {
    margin-top: 2rem;
}

.action-button {
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: none;
    transition: all 0.3s ease;
    min-width: 140px;
}

.primary-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

    .primary-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

.delete-button {
    border: 2px solid #ef4444;
    color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

    .delete-button:hover {
        background: rgba(239, 68, 68, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
    }

/* Utility Classes */
.text-muted {
    color: #6b7280 !important;
}

/* Responsive Design */
@@media only screen and (max-width: 1200px) {
    .patient-stepper-section {
        width: 95%;
    }

    #patient-step-content {
        width: 93%;
    }
}

@@media only screen and (max-width: 768px) {
    .patient-stepper-section {
        width: 100%;
        margin: 20px auto;
    }

    #patient-step-content {
        width: auto;
        margin: auto 3%;
    }
}

@@media (max-width: 768px) {
    .photo-upload-section, .uploaded-photo-section {
        padding: 1rem;
    }

    .page-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .upload-dropzone {
        padding: 1.5rem;
    }

    .action-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .card-content-padding {
        padding: 1rem;
    }

    .step-navigation {
        flex-direction: column;
        gap: 0.5rem;
    }

    .step-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

@@media (max-width: 480px) {
    .patient-stepper-section {
        margin: 10px auto;
    }

    #patient-step-content {
        margin: auto 2%;
    }

    .step-button {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        min-width: 120px;
    }

    .patient-management-container .mud-container {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
}
