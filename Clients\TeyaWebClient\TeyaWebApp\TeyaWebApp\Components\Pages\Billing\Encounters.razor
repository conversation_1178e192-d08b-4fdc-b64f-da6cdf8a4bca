﻿@page "/BillingEncounters"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaUIModels.ViewModel
@using TeyaWebApp.Authorization
@attribute [Authorize(Policy = "NotesAccessPolicy")]
@inject IStringLocalizer<TeyaAIScribeStrings> Localizer
@using System.Text.Json
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.RichTextEditor
@using TeyaWebApp.Components.Layout
@using TeyaWebApp.Services
@using TeyaWebApp.TeyaAIScribeResources
@using MudBlazor
@inject IDialogService DialogService
@inject IMemberService MemberService
@inject IPredefinedTemplateService PredefinedTemplateService
@inject ITokenService TokenService
@inject IOrganizationService OrganizationService
@inject IUserLicenseService UserLicenseService
@inject IPlanTypeService PlanTypeService
@inject IProgressNotesService ProgressNotesService
@inject HttpClient Http
@layout Admin
@using Markdig

<GenericCard>
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4 my-gen-card">
        <div class="filter-container mb-3">
            <MudPaper Class="pa-2" Elevation="1">
                <div class="d-flex flex-wrap align-items-center gap-2">
                    <MudTextField @bind-Value="patientFilter"
                                  Placeholder="Search By Patient Name . . . "
                                  Label="Patient"
                                  Variant="Variant.Outlined"
                                  Class="me-2"
                                  Immediate="true"
                                  Margin="Margin.Dense"
                                  Style=" height: 40px;" />
                    
                    <MudTextField @bind-Value="providerFilter"
                                  Placeholder="Search By Provider Name . . . "
                                  Label="Provider"
                                  Variant="Variant.Outlined"
                                  Class="me-2"
                                  Immediate="true"
                                  Margin="Margin.Dense"
                                  Style="height: 40px;" />
                    <MudDatePicker @bind-Date="dateFromFilter"
                                   Label="From Date"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense"
                                    />

                    <MudDatePicker @bind-Date="dateToFilter"
                                   Label="To Date"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense"
                                    />
                    <!-- Editable Records Filter -->
                    <MudSelect T="bool?" @bind-Value="isEditableFilter"
                               Label="Editable Records"
                               Variant="Variant.Outlined"
                               Margin="Margin.Dense"
                               Style="width: 180px; height: 40px;">
                        <MudSelectItem T="bool?" Value="@null">All Records</MudSelectItem>
                        <MudSelectItem T="bool?" Value="@true">Editable Only</MudSelectItem>
                        <MudSelectItem T="bool?" Value="@false">Non-Editable Only</MudSelectItem>
                    </MudSelect>
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Primary"
                               Size="Size.Small"
                               Style="height:33px;"
                               OnClick="ResetFilters">
                        Reset
                    </MudButton>
                </div>
            </MudPaper>
        </div>

        <div class="grid-container">
            <SfGrid DataSource="@filteredEncounters" AllowPaging="true" GridLines="GridLine.Both">
                <GridEvents TValue="Record" ></GridEvents>

                <GridEditSettings AllowAdding="true" AllowEditing="false" AllowDeleting="false"></GridEditSettings>
                <GridColumns>

                    <GridColumn Field=@nameof(Record.DateTime) HeaderText="Date" Width="100" AllowEditing="false" TextAlign="TextAlign.Center"
                                Format="MMM dd, yyyy">
                    </GridColumn>
                    <GridColumn Field=@nameof(Record.PCPId) HeaderText="Provider" Width="120" TextAlign="TextAlign.Center">
                        <Template>
                            @{
                                var record = (Record)context;
                                var provider = AllMembers.FirstOrDefault(p => p.Id == record.PCPId);
                                @($"{provider?.FirstName} {provider?.LastName}")
                            }
                        </Template>
                    </GridColumn>
                    <GridColumn Field=@nameof(Record.PatientName) HeaderText="Patient" Width="100" AllowEditing="false" TextAlign="TextAlign.Center">
                    </GridColumn>
                </GridColumns>
            </SfGrid>
        </div>
    </MudContainer>
</GenericCard>



<style>

    .my-gen-card {
        padding: 0px !important;
    }

    .mud-card-header {
        padding: 0 !important;
    }

    .mud-card-content {
        padding: 0px !important;
        padding-left: 2% !important;
    }

    /* Grid Rows */
    .e-row {
        transition: background-color 0.2s ease;
        border-bottom: 1px solid #f0f0f0;
    }

        .e-row:hover {
            background-color: #f5faff;
            cursor: pointer;
        }

    .e-altrow {
        background-color: #fcfcfc;
    }
</style>
