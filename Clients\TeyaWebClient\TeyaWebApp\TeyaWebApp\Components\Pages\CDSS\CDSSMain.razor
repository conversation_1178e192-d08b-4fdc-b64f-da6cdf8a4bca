﻿@page "/cdss"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using TeyaWebApp.Authorization
@attribute [Authorize]
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@using Syncfusion.Blazor.Buttons
@using TeyaUIModels.Model
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs
@inject IDialogService DialogService
@inject NavigationManager Navigation

<MudPaper Class="p-4">
    <MudText Typo="Typo.h5">Patient Specific Alerts</MudText>
    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@CreateAlert">+ Create New Alert</MudButton>

    <SfGrid DataSource="@AlertList" AllowPaging="true" AllowSorting="true" >
        <GridPageSettings PageSize="10"></GridPageSettings>
        <GridColumns>
            <GridColumn Field=@nameof(PatientSpecificAlertsData.AlertName) HeaderText="Alert" Width="150"></GridColumn>
            <GridColumn Field=@nameof(PatientSpecificAlertsData.LastDone) HeaderText="Last Done" Width="120" Type="ColumnType.Date"></GridColumn>
            <GridColumn Field=@nameof(PatientSpecificAlertsData.Frequency) HeaderText="Frequency" Width="100"></GridColumn>
            <GridColumn Field=@nameof(PatientSpecificAlertsData.DueDate) HeaderText="Due Date" Width="120" Type="ColumnType.Date"></GridColumn>
            <GridColumn Field=@nameof(PatientSpecificAlertsData.Orders) HeaderText="Orders" Width="100"></GridColumn>
            <GridColumn Field=@nameof(PatientSpecificAlertsData.IsActive) HeaderText="Status" Width="100"></GridColumn>
        </GridColumns>
    </SfGrid>
</MudPaper>
