﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;
using Syncfusion.Blazor.DropDowns;
using Microsoft.Extensions.Localization;
using TeyaWebApp.TeyaAIScribeResources;


namespace TeyaWebApp.Components.Pages
{
    public partial class PreventiveMedicine : Microsoft.AspNetCore.Components.ComponentBase
    {

        [Inject] private IPreventiveMedicineService _preventiveMedicineService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        private Guid patientId { get; set; }
     
        private Guid SelectedCategoryId { get; set; }
        private Guid SelectedSubCategoryId { get; set; }
        private Guid SelectedSymptomId { get; set; }


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }

        private SfRichTextEditor RichTextEditor;
        private SfGrid<PreventiveMedicines> PreventiveMedicineGrid;
        private MudDialog _preventiveMedicineDialog;
        private string editorContent;

        /// <summary>
        /// Lists to hold preventive medicine entries and their original state for comparison and restoration after cancelation.
        /// </summary>
        private List<PreventiveMedicines> preventiveMedicineEntries = new List<PreventiveMedicines>();
        private List<PreventiveMedicines> originalEntries = new List<PreventiveMedicines>();

        /// <summary>
        /// Lists to hold categories, subcategories, and symptoms for the dropdowns.
        /// </summary>
        private List<PMCategory> Categories = new List<PMCategory>();
        private List<PMSubCategory> SubCategories = new List<PMSubCategory>();
        private List<PMSymptoms> Symptoms = new List<PMSymptoms>();

        /// <summary>
        /// Filtered lists to show only relevant subcategories and symptoms based on the selected category and subcategory.
        /// </summary>
        private List<PMSubCategory> FilteredSubCategories = new List<PMSubCategory>();
        private List<PMSymptoms> FilteredSymptoms = new List<PMSymptoms>();


        /// <summary>
        /// Lists to keep track of added, updated, and deleted entries.
        /// </summary>
        private List<PreventiveMedicines> addList = new List<PreventiveMedicines>();
        private List<PreventiveMedicines> updateList = new List<PreventiveMedicines>();
        private List<PreventiveMedicines> deleteList = new List<PreventiveMedicines>();


        /// <summary>
        /// Initializes the component and loads the preventive medicine entries, categories, subcategories, and symptoms.
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
             patientId=PatientID;
            await LoadPreventiveMedicineEntries();
            await LoadCategories();
            await LoadSubCategories();
            await LoadSymptoms();
        }

        /// <summary>
        /// Loads the preventive medicine entries for the patient and sets up the original entries
        /// </summary>
        /// <returns></returns>
        private async Task LoadPreventiveMedicineEntries()
        {
            preventiveMedicineEntries = await _preventiveMedicineService.GetPreventiveMedicinesByPatientIdAndIsActiveAsync(patientId, OrgId,false);
            preventiveMedicineEntries = preventiveMedicineEntries.OrderByDescending(e => e.CreatedDate).ToList();
            originalEntries = preventiveMedicineEntries.Select(e => ClonePreventiveMedicineEntry(e)).ToList();
            editorContent = string.Join("<p>", preventiveMedicineEntries.Select(e =>
                $"<strong>{e.CreatedDate.ToString("MM-dd-yyyy")}: </strong>{e.Category} - {e.SubCategory}- {e.Symptoms} - {e.Detection}-{e.Notes}"));
        }

        private async Task LoadCategories()
        {
            Categories = (await _preventiveMedicineService.GetAllPMCategoriesAsync()).ToList();
        }

        private async Task LoadSubCategories()
        {
            SubCategories = (await _preventiveMedicineService.GetAllPMSubCategoriesAsync()).ToList();
        }

        private async Task LoadSymptoms()
        {
            Symptoms = (await _preventiveMedicineService.GetAllPMSymptomsAsync()).ToList();
        }


        private PreventiveMedicines ClonePreventiveMedicineEntry(PreventiveMedicines entry)
        {
            return new PreventiveMedicines
            {
                PreventiveMedicineId = entry.PreventiveMedicineId,
                PatientId = entry.PatientId,
                OrganisationId = entry.OrganisationId,
                PCPId = entry.PCPId,
                CreatedDate = entry.CreatedDate,
                UpdatedDate = entry.UpdatedDate,
                IsActive = entry.IsActive,
                Category = entry.Category,
                SubCategory = entry.SubCategory,
                Symptoms = entry.Symptoms,
                Detection = entry.Detection,
                Notes = entry.Notes,
                SelectedCategoryId = entry.SelectedCategoryId,
                SelectedSubCategoryId = entry.SelectedSubCategoryId,
                SelectedSymptomId = entry.SelectedSymptomId
            };
        }


        private string SelectedCategory { get; set; } = string.Empty;
        private string SelectedSubCategory { get; set; } = string.Empty;
        private string SelectedSymptom { get; set; } = string.Empty;

        /// <summary>
        /// Handles the change event for the category dropdown. It filters the subcategories and symptoms based on the selected category.
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        private async Task OnCategoryChange(ChangeEventArgs<string, PMCategory> args)
        {
            SelectedSubCategory = string.Empty;
            SelectedSymptom = string.Empty;

            // Get the selected category ID based on the selected name
            var selectedCategory = Categories.FirstOrDefault(c => c.PMCategoryName == SelectedCategory);

            FilteredSubCategories = selectedCategory != null
                ? SubCategories.Where(sc => sc.PMCategoryId == selectedCategory.PMCategoryId).ToList()
                : new List<PMSubCategory>();

            FilteredSymptoms = new List<PMSymptoms>();
            await InvokeAsync(StateHasChanged);
        }

        /// <summary>
        /// Handles the change event for the subcategory dropdown. It filters the symptoms based on the selected subcategory.
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        private async Task OnSubCategoryChange(ChangeEventArgs<string, PMSubCategory> args)
        {
            SelectedSymptom = string.Empty;

            // Get the selected subcategory ID based on the selected name
            var selectedSubCategory = FilteredSubCategories.FirstOrDefault(sc => sc.PMSubcategoryName == SelectedSubCategory);

            FilteredSymptoms = selectedSubCategory != null
                ? Symptoms.Where(s => s.PMSubCategoryId == selectedSubCategory.PMSubcategoryId).ToList()
                : new List<PMSymptoms>();

            await InvokeAsync(StateHasChanged);
        }


        /// <summary>
        /// Adds a new entry to the preventive medicine list. 
        /// It checks for duplicates and validates the selected category, subcategory, and symptom.
        /// </summary>
        /// <returns></returns>
        private async Task AddNewEntry()
        {

            if (string.IsNullOrEmpty(SelectedCategory))
            {
                Snackbar.Add(Localizer["SelectCategory"], Severity.Warning);
                return;
            }

            if (string.IsNullOrEmpty(SelectedSubCategory))
            {
                Snackbar.Add(Localizer["SelectSubCategory"], Severity.Warning);
                return;
            }

            if (string.IsNullOrEmpty(SelectedSymptom))
            {
                Snackbar.Add(Localizer["SelectSymptom"], Severity.Warning);
                return;
            }


            // Check for duplicate entry
            bool isDuplicate = preventiveMedicineEntries.Any(e =>
                e.Category == SelectedCategory &&
                e.SubCategory == SelectedSubCategory &&
                e.Symptoms == SelectedSymptom);

            if (isDuplicate)
            {
                Snackbar.Add(Localizer["CheckDuplicates"], Severity.Warning);
                return;
            }
            var newEntry = new PreventiveMedicines
            {
                PreventiveMedicineId = Guid.NewGuid(),
                PatientId = patientId,
                OrganisationId = Guid.Parse(User.id),
                PCPId = Guid.Parse(User.id),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                IsActive = true,
                Category = SelectedCategory,
                SubCategory = SelectedSubCategory,
                Symptoms = SelectedSymptom,
                Detection = null,
                Notes = null,
                SelectedCategoryId = SelectedCategoryId,
                SelectedSubCategoryId = SelectedSubCategoryId,
                SelectedSymptomId = SelectedSymptomId
            };

            addList.Add(newEntry);
            preventiveMedicineEntries.Add(newEntry);
            await PreventiveMedicineGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Resets the input fields for category, subcategory, and symptom.
        /// </summary>
        private void ResetInputFields()
        {
            SelectedCategory = string.Empty;
            SelectedSubCategory = string.Empty;
            SelectedSymptom = string.Empty;
            FilteredSubCategories = new List<PMSubCategory>();
            FilteredSymptoms = new List<PMSymptoms>();
        }


        /// <summary>
        /// Handles the action completed event of the grid. It updates the original entries list with the modified data.
        /// </summary>
        /// <param name="args"></param>
        private void ActionCompletedHandler(Syncfusion.Blazor.Grids.ActionEventArgs<PreventiveMedicines> args)
        {

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                var existingEntry = originalEntries.FirstOrDefault(e =>
                    e.PreventiveMedicineId == args.Data.PreventiveMedicineId &&
                    e.PatientId == args.Data.PatientId);

                if (existingEntry != null)
                {
                    updateList.Add(args.Data);
                }
            }
        }

        /// <summary>
        /// Handles the action begin event of the grid. It validates the input data before saving or deleting.
        /// Also , it shows a confirmation dialog before deleting an entry.
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        private async Task ActionBeginHandler(Syncfusion.Blazor.Grids.ActionEventArgs<PreventiveMedicines> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Validate Notes
                if (!string.IsNullOrEmpty(args.Data.Notes) && !IsAlphabeticWithSpaces(args.Data.Notes))
                {
                    Snackbar.Add(@Localizer["Val.Notes"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                // Validate Detection
                if (!string.IsNullOrEmpty(args.Data.Detection) && !IsAlphabeticWithSpaces(args.Data.Detection))
                {
                    Snackbar.Add(@Localizer["Val.Detection"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }


            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                deleteList.Add(args.Data);
            }
        }

        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s]+$");
        }

        /// <summary>
        /// Saves the changes made to the preventive medicine entries. It adds, updates, or deletes entries based on the lists.
        /// It stores the changes in the database.
        /// </summary>
        /// <returns></returns>
        private async Task SaveChanges()
        {
            try
            {
                if (addList.Any())
                {
                    await _preventiveMedicineService.AddPreventiveMedicineAsync(addList, OrgId, false);
                    addList.Clear();
                }

                if (updateList.Any())
                {
                    await _preventiveMedicineService.UpdatePreventiveMedicinesInBulk(updateList, OrgId, false);
                    updateList.Clear();
                }

                if (deleteList.Any())
                {
                    await _preventiveMedicineService.UpdatePreventiveMedicinesInBulk(deleteList, OrgId, false);
                    deleteList.Clear();
                }

                await LoadPreventiveMedicineEntries();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
              
                Snackbar.Add(Localizer["ErrorSaving"], Severity.Error);
            }
            ClosePreventiveMedicineDialog();
        }

        /// <summary>
        /// Cancels the changes made to the preventive medicine entries. It restores the original entries and clears the lists.
        /// </summary>
        /// <returns></returns>
        private async Task CancelChanges()
        {
            preventiveMedicineEntries = originalEntries.Select(e => ClonePreventiveMedicineEntry(e)).ToList();
            addList.Clear();
            updateList.Clear();
            deleteList.Clear();
            ResetInputFields();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            await InvokeAsync(StateHasChanged);
            ClosePreventiveMedicineDialog();
        }

        /// <summary>
        /// Handles the click event on the backdrop. It shows a message indicating that the backdrop is disabled.
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Opens the preventive medicine dialog. It initializes the dialog and sets up the toolbar items.
        /// </summary>
        private void OpenPreventiveMedicineDialog()
        {
            _preventiveMedicineDialog.ShowAsync();
        }

        /// <summary>
        /// Closes the preventive medicine dialog. It resets the input fields and clears the lists.
        /// </summary>
        private void ClosePreventiveMedicineDialog()
        {
            ResetInputFields();
            _preventiveMedicineDialog.CloseAsync();
        }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
                new ToolbarItemModel() { Command = ToolbarCommand.Bold },
                new ToolbarItemModel() { Command = ToolbarCommand.Italic },
                new ToolbarItemModel() { Command = ToolbarCommand.Underline },
                new ToolbarItemModel() { Command = ToolbarCommand.FontName },
                new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
                new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
                new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
                new ToolbarItemModel() { Command = ToolbarCommand.Undo },
                new ToolbarItemModel() { Command = ToolbarCommand.Redo },
                new ToolbarItemModel() { Name = "add" },
        };
    }
}
