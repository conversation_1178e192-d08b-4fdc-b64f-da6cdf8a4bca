using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using TeyaUIModels.Model;
using TeyaWebApp.TeyaAIScribeResources;

namespace TeyaWebApp.Components.Pages
{
    public partial class LicenseActivation : ComponentBase
    {
        private List<ProductLicense>? licenses { get; set; }
        private List<Member>? Members { get; set; }

        private bool _drawerOpen = true;

        [Inject]
        private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }

        [Inject]
        private ILogger<LicenseActivation> Logger { get; set; }

        protected override async Task OnInitializedAsync()
        {
            await LoadLicensesAsync();
        }

        private async Task LoadLicensesAsync()
        {
            try
            {
                licenses = await licenseService.GetProductsLicenseAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorFetchingLicenses"]);

            }
        }

        private void UpdateProductAccessLocally(ProductLicense license, object value)
        {
            var hasAccess = (bool)value;
            license.IsLicenseActivated = hasAccess;
        }

        private async Task SaveAllMemberAccess()
        {
            if (licenses != null && licenses.Any())
            {
                try
                {
                    await licenseService.UpdateLicenseAccessAsync(licenses);
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, Localizer["ErrorSavingLicenses"]);
                }
            }
        }

        private void DrawerToggle()
        {
            _drawerOpen = !_drawerOpen;
        }
    }
}