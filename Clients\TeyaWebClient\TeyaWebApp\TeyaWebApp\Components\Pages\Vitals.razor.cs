﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;
using Microsoft.Azure.Amqp.Framing;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;


namespace TeyaWebApp.Components.Pages
{
    public partial class Vitals : Microsoft.AspNetCore.Components.ComponentBase
    {

       
        [Inject] ISnackbar SnackBar { get; set; }

        private Guid patientId { get; set; }
        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }


        [Inject] private UserContext UserContext { get; set; }
        [Inject] ISnackbar Snackbar { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;

        [Inject] IVitalService _VitalService { get; set; }

        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ActiveUser User { get; set; }

        private string editorContent;
        private MudDialog _vitalsdialog;
        private SfRichTextEditor RichTextEditor;
        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>()
        {
        new ToolbarItemModel() { Command = ToolbarCommand.Bold },
        new ToolbarItemModel() { Command = ToolbarCommand.Italic },
        new ToolbarItemModel() { Command = ToolbarCommand.Underline },
        new ToolbarItemModel() { Command = ToolbarCommand.FontName },
        new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
        new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
        new ToolbarItemModel() { Command = ToolbarCommand.Undo },
        new ToolbarItemModel() { Command = ToolbarCommand.Redo },
        new ToolbarItemModel() { Name = "add" },
        new ToolbarItemModel() { Name = "close" },
        };
        private List<string> ToolbarItems = new List<string> { "Add" };
        public string temperature { get; set; }
        public string weight { get; set; }
        public string height { get; set; }
        public string pulse { get; set; }
        private Guid? OrgID { get; set; }
        public string blood_Pressure { get; set; }
        public SfGrid<PatientVitals> VitalsGrid { get; set; }
        private List<PatientVitals> vitals { get; set; }
        private List<PatientVitals> AddList = new();
        private List<PatientVitals> DeleteList = new();

        /// <summary>
        /// Open Edit Dialog
        /// </summary>
        private void OpenAddTaskDialog()
        {
            _vitalsdialog.ShowAsync();
        }

        /// <summary>
        /// Close Edit Dialog
        /// </summary>
        private void CloseAddTaskDialog()
        {
            _vitalsdialog.CloseAsync();
        }
        bool add = false;

        /// <summary>
        /// Retrieve Vitals and set rich text editor
        /// </summary>
        /// <returns></returns>
        protected override async Task OnInitializedAsync()
        {
            patientId = PatientID;        
            ManualContent = Data;
            OrgID = OrgId;

            Subscription = UserContext.ActiveUserSubscription;
            vitals = await _VitalService.GetVitalsByIdAsyncAndIsActive(patientId, OrgID, Subscription);
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        /// <summary>
        /// Event Handler for add,edit,delete
        /// </summary>
        /// <param name="args"></param>
        private void ActionCompletedHandler(ActionEventArgs<PatientVitals> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                if (args.Data != null)
                {
                    var deletedVitals = args.Data as PatientVitals;
                    var existingItem = AddList.FirstOrDefault(v => v.VitalId == deletedVitals.VitalId);

                    if (existingItem != null)
                    {
                        AddList.Remove(existingItem);
                    }
                    else
                    {
                        deletedVitals.isActive = false;
                        deletedVitals.UpdatedBy = Guid.Parse(User.id);
                        deletedVitals.UpdatedDate = DateTime.Now;    
                        DeleteList.Add(deletedVitals);
                    }
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.VitalId = Guid.NewGuid();
                args.Data.PatientId = patientId;
                args.Data.OrganizationId = OrgID ?? Guid.Empty;
                args.Data.PCPId = Guid.Parse(User.id);
                args.Data.CreatedBy = Guid.Parse(User.id);
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.CreatedDate = DateTime.Now;
                args.Data.UpdatedDate = DateTime.Now;
                args.Data.isActive = true;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                if (add)
                {
                    if (args.Data != null)
                    {
                        var addedVitals = args.Data;
                        if (addedVitals != null)
                        {
                            AddList.Add(addedVitals); // Add to AddList
                        }
                    }
                    add = false;
                }
                args.Data.UpdatedBy = Guid.Parse(User.id);
                args.Data.UpdatedDate = DateTime.Now;


            }
        }

        private async Task ActionBeginHandler(ActionEventArgs<PatientVitals> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
               
            }

           else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                bool hasValidationError = false;
            
                if (!string.IsNullOrEmpty(args.Data.Temperature?.ToString()) && !IsNumeric(args.Data.Temperature))
                {
                    Snackbar.Add(@Localizer["Validation.TemperatureNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                // Uncomment and use if BP validation is needed
                //if (!string.IsNullOrEmpty(args.Data.BP?.ToString()) && !IsNumeric(args.Data.BP))
                //{
                //    Snackbar.Add(@Localizer["Validation.BPNumericOnly"], Severity.Warning);
                //    hasValidationError = true;
                //}
            
                if (!string.IsNullOrEmpty(args.Data.Pulse?.ToString()) && !IsNumeric(args.Data.Pulse))
                {
                    Snackbar.Add(@Localizer["Validation.PulseNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                if (!string.IsNullOrEmpty(args.Data.Weight?.ToString()) && !IsNumeric(args.Data.Weight))
                {
                    Snackbar.Add(@Localizer["Validation.WeightNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                if (!string.IsNullOrEmpty(args.Data.Height?.ToString()) && !IsNumeric(args.Data.Height))
                {
                    Snackbar.Add(@Localizer["Validation.HeightNumericOnly"], Severity.Warning);
                    hasValidationError = true;
                }
            
                if (hasValidationError)
                {
                    args.Cancel = true;
                    return;
                }
            }

        }

        private bool IsNumeric(object value)
        {
            return double.TryParse(value?.ToString(), out _);
        }


        /// <summary>
        /// Handle BackdropClick
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        /// <summary>
        /// Save changes to database
        /// </summary>
        /// <returns></returns>
        private async Task SaveChanges()
        {
            bool? saveResult = await DialogService.ShowMessageBox(
                Localizer["Confirm Save"],
                Localizer["Are you sure you want to save these changes?"],
                yesText: Localizer["Yes"],
                noText: Localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }
            if (AddList.Count != 0)
            {
                await _VitalService.AddVitalAsync(AddList, OrgID, Subscription);
            }
            await _VitalService.UpdateVitalsListAsync(DeleteList, OrgID, Subscription);
            await _VitalService.UpdateVitalsListAsync(vitals, OrgID, Subscription);
            AddList.Clear();
            DeleteList.Clear();
            editorContent = GenerateRichTextContent(ManualContent);
           await HandleDynamicComponentUpdate();
          

            Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }

        /// <summary>
        /// Undo changes
        /// </summary>
        /// <returns></returns>
        private async Task CancelChanges()
        {

            DeleteList.Clear();
            AddList.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            vitals = await _VitalService.GetVitalsByIdAsyncAndIsActive(patientId, OrgID, Subscription);
            await InvokeAsync(StateHasChanged);
            CloseAddTaskDialog();
        }


        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = vitals != null
                ? string.Join(" ",
                    vitals
                        .Where(v => !string.IsNullOrEmpty(v.Temperature) || !string.IsNullOrEmpty(v.BP) ||
                                   !string.IsNullOrEmpty(v.Pulse) || !string.IsNullOrEmpty(v.Height) ||
                                   !string.IsNullOrEmpty(v.Weight))
                        .OrderByDescending(v => v.CreatedDate)
                        .Select(v => $"<ul><li style='margin-left: 20px;'><b>{v.CreatedDate:yyyy-MM-dd}</b> - " +
                                    $"Temperature: {v.Temperature}, " +
                                    $"Blood Pressure: {v.BP}, " +
                                    $"Pulse: {v.Pulse}, " +
                                    $"Height: {v.Height}, " +
                                    $"Weight: {v.Weight}</li></ul>"))
                : string.Empty;

            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }




        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}
