using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;

namespace TeyaWebApp.Components.Pages.CDSS
{
    public partial class CDSSMain
    {
        /// <summary>
        /// Injected service for managing patient data.
        /// </summary>
        [Inject]
        private PatientService PatientService { get; set; }
        /// <summary>
        /// Gets the patient data from the service or initializes a new instance if null.
        /// </summary>
        public Patient PatientData => PatientService.PatientData ?? new Patient();
        private List<PatientSpecificAlertsData> AlertList = new();
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPatientSpecificAlertsService PatientSpecificAlertsService { get; set; }
        [Inject] private ILogger<CDSSMain> Logger { get; set; }

        /// <summary>
        /// Initializes the component and loads the necessary data.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            try
            {
                var alerts = await PatientSpecificAlertsService.GetActivePatientSpecificAlertsAsync(PatientData.Id);
                AlertList = alerts.ToList();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error : {ex.Message}");
            }
        }

        void CreateAlert() => Navigation.NavigateTo("/cdss/create-alert");
    }
}