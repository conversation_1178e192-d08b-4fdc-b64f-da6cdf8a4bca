﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Azure.Amqp.Framing;
using MudBlazor;
using Syncfusion.Blazor.DropDowns;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class SubjectiveReviewOfSystem
    {
        [Inject] ISnackbar SnackBar { get; set; }
        [Inject] public IReviewOfSystemService _ReviewOfSytemService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ActiveUser User { get; set; }
      
        private MudDialog _reviewofsystem;
        private DateTime? _CreatedDate = DateTime.Now;
        private DateTime? _UpdatedDate=DateTime.Now;
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        private List<ReviewOfSystem> reviewofsytem { get; set; } = new List<ReviewOfSystem>();
        private SfRichTextEditor RichTextEditor;
        public SfGrid<ReviewOfSystem> ReviewOfSytemGrid { get; set; }
        public Guid PatientId { get; set; }
        private string editorContent;
        private Guid? OrgID { get; set; }
        public List<ReviewOfSystem> deleteList = new List<ReviewOfSystem>();
        public List<ReviewOfSystem> AddList = new List<ReviewOfSystem>();
        private string? _Description = "Enter Additional Reviews";


        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }
        /// <summary>
        /// Initializes the component asynchronously by setting the Patient ID,
        /// fetching the active review of system records, and formatting the editor content.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;          
            ManualContent = Data;
            OrgID = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            reviewofsytem = await _ReviewOfSytemService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            reviewofsytem = reviewofsytem.OrderByDescending(h => h.UpdatedDate).ToList();
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }


        private List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel() { Command = ToolbarCommand.Bold },
            new ToolbarItemModel() { Command = ToolbarCommand.Italic },
            new ToolbarItemModel() { Command = ToolbarCommand.Underline },
            new ToolbarItemModel() { Command = ToolbarCommand.FontName },
            new ToolbarItemModel() { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel() { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel() { Command = ToolbarCommand.Undo },
            new ToolbarItemModel() { Command = ToolbarCommand.Redo },
            new ToolbarItemModel() { Name = "Symbol", TooltipText = "Add Details" },
            new ToolbarItemModel() { Name = "close" },
        };
        /// <summary>
        /// Creates a dropdown template for selecting boolean values in the ReviewOfSystem grid.
        /// </summary>
        private RenderFragment<object> CreateDropdownTemplate(Func<ReviewOfSystem, bool?> getValue, Action<ReviewOfSystem, bool?> setValue)
        {
            return (context) => (builder) =>
            {
                if (context is not ReviewOfSystem review) return;

                builder.OpenComponent<SfDropDownList<bool?, bool?>>(0);
                builder.AddAttribute(1, "DataSource", new List<bool?> { true, false });
                builder.AddAttribute(2, "Value", getValue(review));
                builder.AddAttribute(3, "ValueChanged", EventCallback.Factory.Create<bool?>(this, value => setValue(review, value)));
                builder.AddAttribute(4, "Placeholder", "Select Option");
                builder.CloseComponent();
            };
        }
        /// <summary>
        /// Creates a dropdown template for selecting boolean values in the ReviewOfSystem grid.
        /// </summary>
        private async Task HandleBackdropClick()
        {
            SnackBar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }
        private RenderFragment<object> IsChestPainEditTemplate => CreateDropdownTemplate(
            review => review.ChestPain,
            (review, value) => review.ChestPain = value
        );

        private RenderFragment<object> IsCongestionEditTemplate => CreateDropdownTemplate(
            review => review.Congestion,
            (review, value) => review.Congestion = value
        );

        private RenderFragment<object> IsItchyEyesEditTemplate => CreateDropdownTemplate(
            review => review.ItchyEyes,
            (review, value) => review.ItchyEyes = value
        );
        /// <summary>
        /// Opens the new dialog box .
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task OpenNewDialogBox()
        {
            await _reviewofsystem.ShowAsync();
        }

        /// <summary>
        /// Closes the new dialog box after resetting input fields.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task CloseNewDialogBox()
        {
            ResetInputFields();
            await _reviewofsystem.CloseAsync();
        }

        /// <summary>
        /// Adds a new history record to the ReviewOfSystem list and refreshes the grid.
        /// </summary>
        private async void AddNewHistory()
        {
            var newHistory = new ReviewOfSystem
            {
                ReviewOfSystemId = Guid.NewGuid(),
                PatientId = PatientId,
                PcpId = Guid.Parse(User.id),
                OrganizationId = OrgID ?? Guid.Empty,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now,
                Decription = _Description,
                IsActive = true,
            };

            AddList.Add(newHistory);
            reviewofsytem.Add(newHistory);
            await ReviewOfSytemGrid.Refresh();
            ResetInputFields();
        }

        /// <summary>
        /// Resets the input fields to their default values.
        /// </summary>
        private void ResetInputFields()
        {
            _CreatedDate = null;
        }



        /// <summary>
        /// Handles the action completion event for the ReviewOfSystem grid.
        /// If an item is deleted, it is either removed from the add list or marked as inactive and added to the delete list.
        /// </summary>
        /// <param name="args">Event arguments containing the action details.</param>
        public async Task ActionCompletedHandler(ActionEventArgs<ReviewOfSystem> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deleteReviewOfSystem = args.Data as ReviewOfSystem;
                var existingItem = AddList.FirstOrDefault(m => m.ReviewOfSystemId == deleteReviewOfSystem.ReviewOfSystemId);
                if (existingItem != null)
                {
                    AddList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteList.Add(args.Data);
                }
            }

            //Add a new record
            
            else  if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                var newHistory = new ReviewOfSystem
                {
                    ReviewOfSystemId = Guid.NewGuid(),
                    PatientId = PatientID,
                    PcpId = PatientId,
                    OrganizationId = OrgId,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now,
                    Decription = _Description,
                    IsActive = true,
                };

                AddList.Add(newHistory);
                reviewofsytem.Add(newHistory);
                await ReviewOfSytemGrid.Refresh();
                ResetInputFields();
            }

        }

        /// <summary>
        /// Handles the beginning of an action for the ActiveMedication grid.
        /// If the action is a save operation, it updates the UpdatedDate.
        /// </summary>
        /// <param name="args">Event arguments containing the action details.</param>
        public async Task ActionBeginHandler(ActionEventArgs<ReviewOfSystem> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
                var today = DateTime.Now.Date;

                    if (args.Data.CreatedDate > today)
                    {
                        SnackBar.Add(Localizer["ValidateCreateDate"], Severity.Error);
                        args.Cancel = true;
                    }
            }
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {

                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                
            }
        }

        /// <summary>
        /// Cancels the data operation, clears lists, reloads data, and resets input fields.
        /// </summary>
        private async Task CancelData()
        {
            deleteList.Clear();
            AddList.Clear();
            reviewofsytem = await _ReviewOfSytemService.GetAllByIdAndIsActiveAsync(PatientId, OrgID, Subscription);
            ResetInputFields();
            SnackBar.Add(Localizer["ChangesCancelled"], Severity.Info);
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        /// <summary>
        /// Saves the data by adding new items and updating existing ones.
        /// Updates the editor content with formatted review system data.
        /// </summary>
        private async Task SaveData()
        {
            if (AddList.Count != 0)
            {
                await _ReviewOfSytemService.AddReviewOfSystemAsync(AddList, OrgID, Subscription);
            }
            await _ReviewOfSytemService.UpdateReviewOfSystemListAsync(reviewofsytem,OrgID, Subscription);
            await _ReviewOfSytemService.UpdateReviewOfSystemListAsync(deleteList, OrgID, Subscription);
            deleteList.Clear();
            AddList.Clear();
            editorContent = GenerateRichTextContent(ManualContent);
            await HandleDynamicComponentUpdate();
            SnackBar.Add(Localizer["RecordSaved"], Severity.Success);
            await InvokeAsync(StateHasChanged);
            CloseNewDialogBox();
        }

        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = reviewofsytem != null
                ? string.Join(" ",
                    reviewofsytem
                        .Where(r => r.ChestPain == true || r.Congestion == true || r.ItchyEyes == true || !string.IsNullOrEmpty(r.Decription))
                        .OrderByDescending(r => r.CreatedDate)
                        .Select(r => $"<ul><li style='margin-left: 20px;'><b>{r.CreatedDate:yyyy-MM-dd}</b> - " +
                                    $"Chest Pain: {r.ChestPain}, " +
                                    $"Congestion: {r.Congestion}, " +
                                    $"Itchy Eyes: {r.ItchyEyes}, " +
                                    $"Description: {r.Decription}</li></ul>"))
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }

        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }

    }
}