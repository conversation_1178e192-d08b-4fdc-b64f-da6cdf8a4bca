﻿using Microsoft.AspNetCore.Components;
using Microsoft.Azure.Amqp.Framing;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Net.NetworkInformation;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class Examinations : ComponentBase
    {
        private SfRichTextEditor RichTextEditor;
        private SfGrid<Examination> ExaminationGrid;
        private MudDialog _examinationDialog;
        private List<Examination> activeExaminationEntries = new List<Examination>();
        private List<Examination> entriesToAdd = new List<Examination>();
        private List<Examination> entriesToUpdate = new List<Examination>();
        private List<Examination> entriesToDelete = new List<Examination>();
        private List<Examination> previousExaminationEntries = new List<Examination>();

       
        [Inject] public ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] public IExaminationService ExaminationService { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private UserContext UserContext { get; set; }

        private string editorContent;
        private Guid patientId { get; set; }

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }
        private Guid PatientId { get; set; }
        private Guid? OrgID { get; set; }
      


        protected override async Task OnInitializedAsync()
        {
            patientId = PatientID;
            OrgID = OrgId;
            ManualContent = Data;

            Subscription = UserContext.ActiveUserSubscription;
            activeExaminationEntries = await ExaminationService.GetExaminationByPatientIdAsyncAndIsActive(patientId, OrgID, Subscription);
            previousExaminationEntries = activeExaminationEntries.Select(CloneExamination).ToList();
            editorContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(editorContent);

        }


        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }

        private async Task LoadExaminationEntriesAsync()
        {
            try
            {
               
                Subscription = UserContext.ActiveUserSubscription;
                activeExaminationEntries = await ExaminationService.GetExaminationByPatientIdAsyncAndIsActive(patientId, OrgID, Subscription);
                previousExaminationEntries = activeExaminationEntries.Select(CloneExamination).ToList();
                //editorContent = GenerateRichTextContent(Data);
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Failed to load entries: {ex.Message}", Severity.Error);
            }
        }

        private Examination CloneExamination(Examination exam) => new Examination
        {
            ExaminationId = exam.ExaminationId,
            OrganizationId = exam.OrganizationId,
            PCPId = exam.PCPId,
            PatientId = exam.PatientId,
            CreatedDate = exam.CreatedDate,
            UpdateDate = exam.UpdateDate,
            GeneralDescription = exam.GeneralDescription,
            HEENT = exam.HEENT,
            Lungs = exam.Lungs,
            Abdomen = exam.Abdomen,
            PeripheralPulses = exam.PeripheralPulses,
            Skin = exam.Skin,
            Others = exam.Others,
            IsActive = exam.IsActive
        };

        /// <summary>
        /// Add a new Record
        /// </summary>
        private void AddNewEntry()
        {
            var newEntry = new Examination
            {
                ExaminationId = Guid.NewGuid(),
                OrganizationId = OrgID ?? Guid.Empty,
                PCPId = Guid.Parse(User.id),
                PatientId = PatientID,
                CreatedDate = DateTime.Now,
                UpdateDate = DateTime.Now,
                GeneralDescription = null,
                HEENT = null,
                Lungs = null,
                Abdomen = null,
                PeripheralPulses = null,
                Skin = null,
                Others = null,
                IsActive = true
            };
            entriesToAdd.Add(newEntry);
            activeExaminationEntries.Add(newEntry);
            ExaminationGrid.Refresh();
            StateHasChanged();
        }
      
        

        public void ActionCompleteHandler(ActionEventArgs<Examination> args)
        {

           
            /// <summary>
            /// Add a new Record
            /// </summary>

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                var newEntry = new Examination
                {
                    ExaminationId = Guid.NewGuid(),
                    OrganizationId = OrgID ?? Guid.Empty,
                    PCPId = Guid.Parse(User.id),
                    PatientId = patientId,
                    CreatedDate = DateTime.Now,
                    UpdateDate = DateTime.Now,
                    GeneralDescription = null,
                    HEENT = null,
                    Lungs = null,
                    Abdomen = null,
                    PeripheralPulses = null,
                    Skin = null,
                    Others = null,
                    IsActive = true
                };
                entriesToAdd.Add(newEntry);
                activeExaminationEntries.Add(newEntry);
                StateHasChanged();
                ExaminationGrid.Refresh();

            }

        }

        public async Task ActionBeginHandler(ActionEventArgs<Examination> args)
        {

          

            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                  Localizer["ConfirmDelete"],
                  Localizer["DeleteConfirmationMessage"],
                  yesText: Localizer["Yes"],
                  noText: Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
                args.Data.IsActive = false;
                entriesToDelete.Add(args.Data);
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {



                // Validated all the fields should take only Aphabates and spces
                if (!string.IsNullOrEmpty(args.Data.GeneralDescription) && !IsAlphabeticWithSpaces(args.Data.GeneralDescription))
                {
                    Snackbar.Add(@Localizer["Validation.GeneralDescriptionAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.HEENT) && !IsAlphabeticWithSpaces(args.Data.HEENT))
                {
                    Snackbar.Add(@Localizer["Validation.HEENTAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.Lungs) && !IsAlphabeticWithSpaces(args.Data.Lungs))
                {
                    Snackbar.Add(@Localizer["Validation.LungsAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.Abdomen) && !IsAlphabeticWithSpaces(args.Data.Abdomen))
                {
                    Snackbar.Add(@Localizer["Validation.AbdomenAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.PeripheralPulses) && !IsAlphabeticWithSpaces(args.Data.PeripheralPulses))
                {
                    Snackbar.Add(@Localizer["Validation.PeripheralPulsesAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }

                if (!string.IsNullOrEmpty(args.Data.Skin) && !IsAlphabeticWithSpaces(args.Data.Skin))
                {
                    Snackbar.Add(@Localizer["Validation.SkinAlphaOnly"], Severity.Warning);
                    args.Cancel = true;
                    return;
                }


                var exam = args.Data;
                exam.UpdateDate = DateTime.Now;
                entriesToUpdate.Add(exam);


                StateHasChanged();
            }

        }

       

        /// <summary>
        /// Method to Validates the field
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        private bool IsAlphabeticWithSpaces(string input)
        {
            return System.Text.RegularExpressions.Regex.IsMatch(input, @"^[a-zA-Z\s]+$");
        }


        /// <summary>
        /// Handle backdrop click
        /// </summary>
        /// <returns></returns>
        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }


        private void UpdateEditorContent()
        {
            var latestEntry = activeExaminationEntries
                .OrderByDescending(e => e.UpdateDate)
                .FirstOrDefault();

            editorContent = latestEntry != null
                ? $"<p><strong>General Description:</strong> {latestEntry.GeneralDescription ?? "N/A"}</p>" +
                  $"<p><strong>HEENT:</strong> {latestEntry.HEENT ?? "N/A"}</p>" +
                  $"<p><strong>Lungs:</strong> {latestEntry.Lungs ?? "N/A"}</p>" +
                  $"<p><strong>Abdomen:</strong> {latestEntry.Abdomen ?? "N/A"}</p>" +
                  $"<p><strong>Peripheral Pulses:</strong> {latestEntry.PeripheralPulses ?? "N/A"}</p>" +
                  $"<p><strong>Skin:</strong> {latestEntry.Skin ?? "N/A"}</p>" +
                  $"<p><strong>Others:</strong> {latestEntry.Others ?? "N/A"}</p>"
                : "<p>No data available</p>";

            RichTextEditor?.RefreshUIAsync();
        }

        /// <summary>
        /// This will Made Permanent Changes to the Database
        /// </summary>
        /// <returns></returns>

        private async Task SaveChanges()
        {
            try
            {
                if (entriesToAdd.Any())
                {
                    await ExaminationService.AddExaminationAsync(entriesToAdd, OrgID, Subscription);
                    entriesToAdd.Clear();
                }

                if (entriesToUpdate.Any())
                {
                    await ExaminationService.UpdateExaminationListAsync(entriesToUpdate, OrgID, Subscription);
                    entriesToUpdate.Clear();
                }

                if (entriesToDelete.Any())
                {
                    await ExaminationService.UpdateExaminationListAsync(entriesToDelete, OrgID, Subscription);
                    entriesToDelete.Clear();
                }

                await LoadExaminationEntriesAsync();
                editorContent = GenerateRichTextContent(ManualContent);
                await HandleDynamicComponentUpdate();
               
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);
                _examinationDialog?.CloseAsync();
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Failed to save changes: {ex.Message}", Severity.Error);
            }
        }

        /// <summary>
        /// Cancel all the Changes and back to its previous state
        /// </summary>
        private void CancelChanges()
        {
            activeExaminationEntries = previousExaminationEntries.Select(CloneExamination).ToList();
            entriesToAdd.Clear();
            entriesToUpdate.Clear();
            entriesToDelete.Clear();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
            _examinationDialog?.CloseAsync();
            //UpdateEditorContent();
            //GenerateRichTextContent(Data);
        }

        public List<ToolbarItemModel> Tools = new List<ToolbarItemModel>
        {
            new ToolbarItemModel { Command = ToolbarCommand.Bold },
            new ToolbarItemModel { Command = ToolbarCommand.Italic },
            new ToolbarItemModel { Command = ToolbarCommand.Underline },
            new ToolbarItemModel { Command = ToolbarCommand.FontName },
            new ToolbarItemModel { Command = ToolbarCommand.FontSize },
            new ToolbarItemModel { Command = ToolbarCommand.OrderedList },
            new ToolbarItemModel { Command = ToolbarCommand.UnorderedList },
            new ToolbarItemModel { Command = ToolbarCommand.Undo },
            new ToolbarItemModel { Command = ToolbarCommand.Redo },
            new ToolbarItemModel { Name = "edit" },
            new ToolbarItemModel() { Name = "close" },
        };

        private void OpenExaminationDialog()
        {
            _examinationDialog?.ShowAsync();
        }

        private string GenerateRichTextContent(string manualData)
        {
            var latestEntry = activeExaminationEntries != null
                ? activeExaminationEntries
                    .Where(e => !string.IsNullOrEmpty(e.GeneralDescription) || !string.IsNullOrEmpty(e.HEENT) ||
                               !string.IsNullOrEmpty(e.Lungs) || !string.IsNullOrEmpty(e.Abdomen) ||
                               !string.IsNullOrEmpty(e.PeripheralPulses) || !string.IsNullOrEmpty(e.Skin) ||
                               !string.IsNullOrEmpty(e.Others))
                    .OrderByDescending(e => e.UpdateDate)
                    .FirstOrDefault()
                : null;
            string dynamicContent = latestEntry != null
                ? string.Join(" ",
                    new[]
                    {
                $"<ul><li style='margin-left: 20px;'><b>General Description:</b> {latestEntry.GeneralDescription ?? "N/A"}</li></ul>",
                $"<ul><li style='margin-left: 20px;'><b>HEENT:</b> {latestEntry.HEENT ?? "N/A"}</li></ul>",
                $"<ul><li style='margin-left: 20px;'><b>Lungs:</b> {latestEntry.Lungs ?? "N/A"}</li></ul>",
                $"<ul><li style='margin-left: 20px;'><b>Abdomen:</b> {latestEntry.Abdomen ?? "N/A"}</li></ul>",
                $"<ul><li style='margin-left: 20px;'><b>Peripheral Pulses:</b> {latestEntry.PeripheralPulses ?? "N/A"}</li></ul>",
                $"<ul><li style='margin-left: 20px;'><b>Skin:</b> {latestEntry.Skin ?? "N/A"}</li></ul>",
                $"<ul><li style='margin-left: 20px;'><b>Others:</b> {latestEntry.Others ?? "N/A"}</li></ul>"
                    })
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }


        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            editorContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            editorContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(editorContent);
            }

        }
    }
}


