﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaUIModels.Model;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Blazor.Grids;
using StackExchange.Redis;
using Microsoft.JSInterop;
using Microsoft.Graph.Models;
using TeyaWebApp.Services;


namespace TeyaWebApp.Components.Pages
{
    public partial class EPrescription : Microsoft.AspNetCore.Components.ComponentBase
    {
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private PatientService _PatientService { get; set; }
        [Inject] private IMemberService _MemberService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        public List<int> RefillOptions { get; set; } = Enumerable.Range(0, 10).ToList();
        public List<string> Providers { get; set; } = new();
        public string PatientName { get; set; }
        public string PatientAddress { get; set; }
        public string DoctorName { get; set; }
        public string PharmacyName { get; set; }
        public string PharmacyAddress { get; set; }
        public string Comments { get; set; }
        public string SupervisingProvider { get; set; }
        private string SelectedEprescriptionType { get; set; }
        private string SelectedResponse { get; set; }
        public int SelectedRefill { get; set; }
        private Guid PatientID { get; set; }
        private Guid orgid { get; set; }
        private bool Subscription = false;
        private async Task SaveAsPDF()
        {
            Snackbar.Add(Localizer["Save as PDF functionality is not implemented yet."], Severity.Info);
        }

        private async Task ShowPreviewRX()
        {
            Snackbar.Add(Localizer["Show Preview RX functionality is not implemented yet."], Severity.Info);
        }

        private async Task SendEPrescription()
        {
            Snackbar.Add(Localizer["Send E-Prescription functionality is not implemented yet."], Severity.Info);
        }

        private async Task Cancel()
        {
            Snackbar.Add(Localizer["Cancel functionality is not implemented yet."], Severity.Info);
        }

        private async Task LinkPharmacy()
        {
            Snackbar.Add(Localizer["Link Pharmacy functionality is not implemented yet."], Severity.Info);
        }

        private async Task RxExternalHistory()
        {
            Snackbar.Add(Localizer["Rx External History functionality is not implemented yet."], Severity.Info);
        }

        private async Task RxEligibility()
        {
            Snackbar.Add(Localizer["Rx Eligibility functionality is not implemented yet."], Severity.Info);
        }

        private async Task PatientHub()
        {
            Snackbar.Add(Localizer["Patient Hub functionality is not implemented yet."], Severity.Info);
        }

        /// <summary>
        /// get list of providers based on organization Id
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            PatientID = _PatientService.PatientData.Id;
            orgid = _PatientService.PatientData.OrganizationID ?? Guid.Empty;
            var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(orgid);
            var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
            Subscription = planType.PlanName == Localizer["Enterprise"];
            Providers = await _MemberService.GetProviderlistAsync(orgid, Subscription);
        }
    }
}