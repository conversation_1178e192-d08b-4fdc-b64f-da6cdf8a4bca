﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using System.Linq;
using TeyaUIModels.Model;
using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using TeyaUIViewModels.ViewModel;
using System.Diagnostics.Eventing.Reader;
using Microsoft.Azure.Amqp.Framing;
using TeyaUIModels.ViewModel;
using TeyaWebApp.Services;

namespace TeyaWebApp.Components.Pages
{
    public partial class ObstetricHistory
    {
        [Inject]
        private ILogger<ObHistoryDTO> Logger { get; set; }
       
        [Inject] private ActiveUser User { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }
        [Inject] private UserContext UserContext { get; set; }
        private Guid activeUserOrganizationId { get; set; }
        private bool Subscription = false;
        [Inject] private IDialogService DialogService { get; set; }
        private SfRichTextEditor richTextEditor;
        private MudDialog showBrowsePopup;
        private SfGrid<ObHistoryDTO> ObHistoryGrid;
        private string richTextContent = string.Empty;
        private string symptoms = string.Empty;
        private string notes = string.Empty;
        private Guid? OrgID { get; set; }
        private Guid PatientId { get; set; }
        private List<ObHistoryDTO> obHistories = new();
        private List<ObHistoryDTO> addedObHistories = new();
        private List<ObHistoryDTO> updatedObHistories = new();
        private List<ObHistoryDTO> deletedObHistories = new();
        private bool add = false;

        private bool isInternalUpdate { get; set; } = false;
        [Parameter] public Guid PatientID { get; set; }
        [Parameter] public Guid OrgId { get; set; }
        [Parameter] public string? Data { get; set; }
        [Parameter] public string? TotalText { get; set; }
        [Parameter] public EventCallback<string> OnValueChanged { get; set; }
        private string? ManualContent {  get; set; }

        protected override async Task OnInitializedAsync()
        {
            PatientId = PatientID;
            ManualContent = Data;
            OrgID = OrgId;
            Subscription = UserContext.ActiveUserSubscription;
            obHistories = await ObHistoryService.LoadObHistoriesAsync(PatientId, OrgID, Subscription);
            richTextContent = GenerateRichTextContent(ManualContent);
            await OnValueChanged.InvokeAsync(richTextContent);
        }

        private bool isEditing = false;
        private int saveInterval { get; set; } = 500;

        private async Task StartEditing()
        {
            isEditing = true;
            await Task.Delay(50); // Small delay to ensure editor is rendered
        }

        private async Task CloseRTE()
        {
            isEditing = false;
        }


        private List<ToolbarItemModel> GetToolbarItems() => new()
        {
            new() { Command = ToolbarCommand.Bold },
            new() { Command = ToolbarCommand.Italic },
            new() { Command = ToolbarCommand.Underline },
            new() { Command = ToolbarCommand.FontName },
            new() { Command = ToolbarCommand.FontSize },
            new() { Command = ToolbarCommand.OrderedList },
            new() { Command = ToolbarCommand.UnorderedList },
            new() { Command = ToolbarCommand.Undo },
            new() { Command = ToolbarCommand.Redo },
            new() { Name = "add", TooltipText = "Insert Symbol" },
            new() { Name = "close" },
        };

        private async Task OnActionBeginHandler(ActionEventArgs<ObHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                // Show confirmation dialog
                bool? result = await DialogService.ShowMessageBox(
                      Localizer["ConfirmDelete"],
                      Localizer["DeleteConfirmationMessage"],
                      yesText: Localizer["Yes"],
                      noText: Localizer["No"]);


                if (result != true)
                {
                    args.Cancel = true;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                // Check for duplicates in existing and added entries
                bool isDuplicate = obHistories.Concat(addedObHistories)
                    .Any(h =>
                        h.Symptoms?.Trim().Equals(args.Data.Symptoms?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        h.Notes?.Trim().Equals(args.Data.Notes?.Trim(), StringComparison.OrdinalIgnoreCase) == true &&
                        h.DateOfComplaint.Date == args.Data.DateOfComplaint.Date &&
                        h.obId != args.Data.obId // ignore self
                    );

                if (isDuplicate)
                {
                    Snackbar.Add(Localizer["Duplicate entry not allowed."], Severity.Error);
                    if (ObHistoryGrid != null)
                        await ObHistoryGrid.CloseEditAsync();
                    //CloseBrowsePopup();
                    args.Cancel = true;
                }

                var today = DateTime.Now.Date;
                if (args.Data.DateOfComplaint > today)
                {
                    Snackbar.Add(Localizer["Validate.CreateDate"], Severity.Error);
                    args.Cancel = true;
                }

            }
        }
        public async Task ActionCompletedHandler(ActionEventArgs<ObHistoryDTO> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                deletedObHistories.Add(args.Data);
                args.Data.IsDeleted = true;
                
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Add)
            {
                args.Data.obId = Guid.NewGuid();
                args.Data.PatientId = PatientId;
                args.Data.OrganizationId = OrgID ?? Guid.Empty;
                args.Data.PcpId = Guid.Parse(User.id);
                args.Data.DateOfComplaint = DateTime.Now;
                add = true;
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {

                if (args.Data.DateOfComplaint > DateTime.Now)
                {
                    Snackbar.Add(Localizer["Future dates are not allowed."], Severity.Error);
                    if (ObHistoryGrid != null)
                        await ObHistoryGrid.CloseEditAsync();
                    CloseBrowsePopup();
                }
               
                if (add)
                {
                    addedObHistories.Add(args.Data);
                    add = false;
                }
                else if (!addedObHistories.Contains(args.Data) && !updatedObHistories.Contains(args.Data))
                {
                    updatedObHistories.Add(args.Data);
                }
            }
        }


        private async Task SaveChanges()
        {
            bool? saveResult = await DialogService.ShowMessageBox(
                Localizer["Confirm Save"],
                Localizer["Are you sure you want to save these changes?"],
                yesText: Localizer["Yes"],
                noText: Localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }
            try
            {
                // Handle new entries
                if (addedObHistories.Any())
                {
                    foreach (var newEntry in addedObHistories)
                    {
                        await ObHistoryService.AddAsync(newEntry, OrgID, Subscription);
                    }
                    addedObHistories.Clear();
                }

                // Handle updates
                if (updatedObHistories.Any())
                {
                    await ObHistoryService.UpdateObHistoryListAsync(updatedObHistories, OrgID, Subscription);
                    updatedObHistories.Clear();
                }

                // Handle deletes
                if (deletedObHistories.Any())
                {
                    await ObHistoryService.UpdateObHistoryListAsync(deletedObHistories, OrgID, Subscription);
                    deletedObHistories.Clear();
                }

                await LoadObHistoriesAsync();
                richTextContent = GenerateRichTextContent(ManualContent);
                await HandleDynamicComponentUpdate();
                await richTextEditor.RefreshUIAsync();
                CloseBrowsePopup();
                Snackbar.Add(Localizer["RecordSaved"], Severity.Success);

            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["SaveError"]);
               
            }
        }

        private async Task HandleBackdropClick()
        {
            Snackbar.Add(Localizer["BackdropDisabledMessage"], Severity.Info);
        }

        private async Task CancelChanges()
        {
            addedObHistories.Clear();
            updatedObHistories.Clear();
            deletedObHistories.Clear();
            await LoadObHistoriesAsync();
            CloseBrowsePopup();
            Snackbar.Add(Localizer["ChangesCancelled"], Severity.Info);
        }

        private async Task LoadObHistoriesAsync()
        {
            try
            {
                obHistories = await ObHistoryService.LoadObHistoriesAsync(PatientId, OrgID, Subscription);
                obHistories = obHistories.OrderByDescending(h => h.DateOfComplaint).ToList();



                if (ObHistoryGrid != null)
                {
                    await ObHistoryGrid.Refresh();
                }


                if (richTextEditor != null)
                    await richTextEditor.RefreshUIAsync();

                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex,Localizer["ErrorLoading"]);
            }
        }

        private string GenerateRichTextContentt() => string.Join(" ",
            obHistories.OrderByDescending(o => o.DateOfComplaint)
                .Select(o => $"<ul><li style='margin-left: 20px;'><b>{o.DateOfComplaint:yyyy-MM-dd}</b> : {o.Symptoms} - {o.Notes}</li></ul>"));


        private void CloseBrowsePopup()
        {
            symptoms = string.Empty;
            notes = string.Empty;
            showBrowsePopup?.CloseAsync();
        }

        private async Task OpenBrowsePopup() => await showBrowsePopup?.ShowAsync();




        private string GenerateRichTextContent(string manualData)
        {
            string dynamicContent = obHistories != null
                ? string.Join(" ", obHistories
                    .Where(o => !string.IsNullOrEmpty(o.Symptoms))
                    .OrderByDescending(o => o.DateOfComplaint)
                    .Select(o => $"<ul><li style='margin-left: 20px;'><b>{o.DateOfComplaint:yyyy-MM-dd}</b>: {o.Symptoms} => {o.Notes}</li></ul>"))
                : string.Empty;
            string userContent = string.IsNullOrWhiteSpace(manualData)
                ? "<div contenteditable='true'>Click to add notes...</div>"
                : $"<div contenteditable='true'>{manualData}</div>";

            return $@"<div>
            {userContent}
            <hr style='border: none; height: 1px; background: transparent; margin: 2px 0;' contenteditable='false' />
            <div contenteditable='false'>
                {dynamicContent}
            </div>
            </div>";
        }


        private async Task HandelRichTextChange(string value)
        {
            if (isInternalUpdate)
            {
                // Reset flag here instead of in finally block
                isInternalUpdate = false;
                return;
            }
            int start = value.IndexOf("<div contenteditable=\"true\">") + "<div contenteditable=\"true\">".Length;
            int end = value.IndexOf("</div>", start);
            ManualContent = value.Substring(start, end - start).Trim();
            richTextContent = value;
            //if (richTextEditor != null)
            //{
            //    await richTextEditor.RefreshUIAsync();
            //}
            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(value);
            }
            await InvokeAsync(StateHasChanged);
        }

        private async Task HandleDynamicComponentUpdate()
        {
            isInternalUpdate = true;
            richTextContent = GenerateRichTextContent(ManualContent);
            await InvokeAsync(StateHasChanged);

            if (OnValueChanged.HasDelegate)
            {
                await OnValueChanged.InvokeAsync(richTextContent);
            }

        }
    }
}
