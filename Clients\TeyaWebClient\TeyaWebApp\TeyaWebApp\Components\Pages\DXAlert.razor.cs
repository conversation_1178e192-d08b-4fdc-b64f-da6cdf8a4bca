﻿using TeyaUIModels.Model;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Localization;
using Markdig.Helpers;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using TeyaUIModels.ViewModel;

namespace TeyaWebApp.Components.Pages
{
    public partial class DXAlert
    {
        [Inject] public IDXAlertService DXAlertService { get; set; }
        [Inject] private ILogger<DiagnosisAlert> _logger { get; set; }
        [Inject] private IStringLocalizer<DiagnosisAlert> _localizer { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService { get; set; }

        [CascadingParameter] private MudDialogInstance? MudDialog { get; set; }

        private SfGrid<DiagnosisAlert>? DiagnosisAlertGrid;

        private List<DiagnosisAlert> dxAlerts = new();
        private List<DiagnosisAlert> deleteAlertList = new();
        private List<DiagnosisAlert> addList = new();
        private List<CompleteOrderSet> OrdersetList;
        private List<CompleteOrderSet> FilteredOrderset;
        private CompleteOrderSet selectedOrderset;
        private bool showNoResultsMessage;

        // Form fields
        private string alertName = string.Empty;
        private string alertDescription = string.Empty;
        private string webReference = string.Empty;
        private int? ageLowerBound;
        private int? ageUpperBound;
        private string orderSet = string.Empty;
        private string? gender = null; 

        [Inject]
        private PatientService PatientService { get; set; } = default!;
        [Inject] IOrderSetService orderSetService { get; set; }
        private Guid PatientId { get; set; }
        private Guid Id { get; set; }
        private Guid? OrgID { get; set; }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Check if patient is selected
                if (PatientService.PatientData != null && PatientService.PatientData.Id != Guid.Empty)
                {
                    // Patient is selected - use patient's organization
                    PatientId = PatientService.PatientData.Id;
                    OrgID = PatientService.PatientData.OrganizationID;
                    _logger.LogInformation($"Patient selected, using patient organization: {OrgID}");
                }
                else
                {
                    // No patient selected - get organization by name and then get organization ID
                    var organization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);
                    if (organization != null && organization.Count > 0)
                    {
                        OrgID = organization[0].OrganizationId;
                        _logger.LogInformation($"No patient selected, using user organization: {OrgID}");
                    }
                    else
                    {
                        _logger.LogWarning($"No organization found with name: {User.OrganizationName}");
                        OrgID = null;
                    }
                }

                // Load order sets first
                OrdersetList = (await orderSetService.GetAllOrderSetAsync()).ToList();

                // Then load alerts
                await LoadAlertsAsync();

                // Force UI update after everything is loaded
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving dx alert data");
                Snackbar?.Add("Error loading diagnosis data. Please try again later.", Severity.Error);
            }
        }

        /// <summary>
        /// Loads diagnosis alerts for the current patient and organization.
        /// Retrieves active alerts from the service and fills the grid with empty rows if needed
        /// to maintain a minimum of 9 rows for display purposes.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task LoadAlertsAsync()
        {
            try
            {
                _logger.LogInformation("Loading all diagnosis alerts from database");
                var existingAlerts = await DXAlertService.GetAllByIdAndIsActiveAsync(Id, OrgID, false);
                _logger.LogInformation($"Found {existingAlerts?.Count() ?? 0} total alerts in database");

                dxAlerts = existingAlerts?.ToList() ?? new List<DiagnosisAlert>();

                // Add empty rows for UI display
                int emptyRowsNeeded = Math.Max(0, 9 - dxAlerts.Count);
                if (emptyRowsNeeded > 0)
                {
                    dxAlerts.AddRange(Enumerable.Range(0, emptyRowsNeeded)
                        .Select(_ => new DiagnosisAlert
                        {
                            Name = string.Empty,
                            Description = string.Empty,
                            WebReference = string.Empty,
                            OrderSet = string.Empty,
                            Gender = string.Empty
                        }));
                }

                // Force grid refresh if it exists
                if (DiagnosisAlertGrid != null)
                {
                    await DiagnosisAlertGrid.Refresh();
                }

                // Trigger UI update
                StateHasChanged();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in LoadAlertsAsync");
                dxAlerts = new List<DiagnosisAlert>();
                Snackbar?.Add("Error loading diagnosis alerts. Please try again later.", Severity.Error);
            }
        }
        public void ActionCompletedHandler(ActionEventArgs<DiagnosisAlert> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                var deletedAlert = args.Data;
                var existingItem = addList.FirstOrDefault(v => v.Id == deletedAlert.Id);

                if (existingItem != null)
                {
                    addList.Remove(existingItem);
                }
                else
                {
                    args.Data.IsActive = false;  // Set IsActive to false instead of actually deleting
                    args.Data.UpdatedDate = DateTime.Now;
                    deleteAlertList.Add(args.Data);
                }
            }
        }
        public async Task ActionBeginHandler(ActionEventArgs<DiagnosisAlert> args)
        {
            if (args.RequestType == Syncfusion.Blazor.Grids.Action.Delete)
            {
                bool? result = await DialogService.ShowMessageBox(
                    @Localizer["Confirm Delete"],
                    @Localizer["Do you want to delete this entry?"],
                    yesText: @Localizer["Yes"],
                    noText: @Localizer["No"]);

                if (result != true)
                {
                    args.Cancel = true;
                    return;
                }
            }
            else if (args.RequestType == Syncfusion.Blazor.Grids.Action.Save)
            {
                args.Data.UpdatedDate = DateTime.Now;
            }
        }
        private async Task SaveData()
        {

            bool? saveResult = await DialogService.ShowMessageBox(
                _localizer["Confirm Save"],
                _localizer["Are you sure you want to save these changes?"],
                yesText: _localizer["Yes"],
                noText: _localizer["No"]);

            if (saveResult != true)
            {
                return; // User canceled
            }
            try
            {
                if (addList.Count > 0)
                {
                    // Set IsActive to true for all new alerts
                    foreach (var alert in addList)
                    {
                        alert.IsActive = true;
                    }
                    await DXAlertService.AddDXAlertsAsync(addList, OrgID, false);
                }

                if (deleteAlertList.Count > 0)
                {
                    await DXAlertService.UpdateDXAlertsListAsync(deleteAlertList, OrgID, false);
                }
                var existingAlerts = dxAlerts.Where(a => !string.IsNullOrEmpty(a.Name) && a.Id != Guid.Empty).ToList();
                if (existingAlerts.Count > 0)
                {
                    await DXAlertService.UpdateDXAlertsListAsync(existingAlerts, OrgID, false);
                }
                deleteAlertList.Clear();
                addList.Clear();

                await LoadAlertsAsync();
                ResetInputFields();

                Snackbar.Add(_localizer["DX alerts saved successfully"], Severity.Success);
                MudDialog?.Close();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving dx alert data");
                Snackbar.Add(_localizer["Failed to save dx alert records"], Severity.Error);
            }
        }
        private async Task CancelData()
        {
            deleteAlertList.Clear();
            addList.Clear();
            await LoadAlertsAsync();
            ResetInputFields();

            // Close the dialog
            MudDialog?.Close();
        }
        private void ResetInputFields()
        {
            alertName = string.Empty;
            alertDescription = string.Empty;
            webReference = string.Empty;
            ageLowerBound = null;
            ageUpperBound = null;
            orderSet = string.Empty;
            gender = null;
        }

        private async Task<IEnumerable<string>> SearchOrderset(string searchTerm, CancellationToken cancellationToken)
        {
            if (searchTerm == null || string.IsNullOrWhiteSpace(searchTerm))
            {
                FilteredOrderset = OrdersetList;
                orderSet = null;
                showNoResultsMessage = false;
                StateHasChanged();
                var result = OrdersetList
                    .Select(temp => temp.orderSet.OrderSetName)
                    .Where(name => !string.IsNullOrEmpty(name))
                    .Distinct()
                    .ToList();
                return result;
            }
            else
            {
                var filtered = OrdersetList
                            .Where(temp => temp.orderSet.OrderSetName != null &&
                                       temp.orderSet.OrderSetName.Contains(searchTerm, StringComparison.InvariantCultureIgnoreCase))
                            .Select(temp => temp.orderSet.OrderSetName)
                            .ToList();

                if (!filtered.Any())
                {
                    showNoResultsMessage = true;
                    return new List<string> { "No orderset Found" };
                }

                showNoResultsMessage = false;
                return filtered;
            }
        }

        private void OnOrdersetSelected(string ordersetName)
        {
            orderSet = ordersetName;
            selectedOrderset = OrdersetList.FirstOrDefault(o => o.orderSet.OrderSetName == ordersetName);
            StateHasChanged();
        }
        private async Task AddNewAlert()
        {
            try
            {
                if (string.IsNullOrEmpty(alertName))
                {
                    Snackbar.Add(_localizer["Please enter alert name"], Severity.Warning);
                    return;
                }
                if (string.IsNullOrEmpty(alertDescription))
                {
                    Snackbar.Add(_localizer["Please add a Description"], Severity.Warning);
                    return;
                }
                if (!ageLowerBound.HasValue || !ageUpperBound.HasValue)
                {
                    Snackbar.Add(_localizer["Please select age lower and upper bound"], Severity.Warning);
                    return;
                }

                if (ageUpperBound.Value == 0)
                {
                    Snackbar.Add(_localizer["Age upper bound should be minimum 1"], Severity.Warning);
                    return;
                }
                if (string.IsNullOrEmpty(gender))
                {
                    Snackbar.Add(_localizer["Please select a gender"], Severity.Warning);
                    return;
                }

                var emptyRow = dxAlerts.FirstOrDefault(i => string.IsNullOrEmpty(i.Name));

                if (emptyRow == null)
                {
                    emptyRow = new DiagnosisAlert();
                    dxAlerts.Add(emptyRow);
                }

                emptyRow.Id = Guid.NewGuid();
                emptyRow.pcpId = Guid.Parse(User.id);
                emptyRow.OrganizationId = OrgID ?? Guid.Empty;
                emptyRow.CreatedDate = DateTime.Now;
                emptyRow.UpdatedDate = DateTime.Now;
                emptyRow.Name = alertName;
                emptyRow.Description = alertDescription;
                emptyRow.WebReference = webReference;
                emptyRow.AgeLowerBound = ageLowerBound;
                emptyRow.AgeUpperBound = ageUpperBound;
                emptyRow.OrdersetId = selectedOrderset?.orderSet?.Id ?? Guid.Empty;
                emptyRow.OrderSet = orderSet ?? string.Empty;
                emptyRow.Gender = gender;
                emptyRow.IsActive = true;  // Set IsActive to true for new alerts

                addList.Add(emptyRow);

                if (DiagnosisAlertGrid != null)
                {
                    await DiagnosisAlertGrid.Refresh();
                }

                ResetInputFields();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new dx alert");
                Snackbar.Add(_localizer["Failed to add dx alert"], Severity.Error);
            }
        }
    }
}
