﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaWebApp.TeyaAIScribeResources;
using static TeyaWebApp.Components.Pages.Encounters;
using TeyaWebApp.ViewModel;
using TeyaWebApp.Components.Pages.CDSS;

namespace TeyaWebApp.Components.Pages
{
    public partial class Alerts
    {
        [Inject] private ILogger<Alerts> Logger { get; set; }
        [Inject] private IStringLocalizer<TeyaAIScribeStrings> Localizer { get; set; }
        [Inject] private IDialogService DialogService { get; set; }
        [Inject] private ISnackbar Snackbar { get; set; }
        [Inject] private IAlertService AlertService { get; set; }
        [Inject] private ActiveUser User { get; set; }
        [Inject] public IImmunizationService _ImmunizationService { get; set; }
        [Inject] IOrderSetService orderSetService { get; set; }
        [Inject] private PatientService PatientService { get; set; }
        [Inject] private IOrganizationService OrganizationService { get; set; }
        [Inject] private IUserLicenseService UserLicenseService  { get; set; }
        [Inject] public ILabTestsService _labTestsService { get; set; }
        [Inject] public IProcedureService ProcedureService { get; set; }
        [Inject] private IPlanTypeService PlanTypeService { get; set; }
        [Inject] private IDiagnosticImagingAlertService DiagnosticImagingAlertService { get; set; }
        [Inject] public ICurrentMedicationService CurrentMedicationService { get; set; }
        [Parameter] public EventCallback<Guid> OnSwitchToNotes { get; set; }
        private readonly IStringLocalizer<TokenService> _localizer;

        private string searchString = "";
        private string selectedSeverity = "All";
        private bool dialogVisible = false;
        private Alert selectedAlert = null;
        private List<Alert> alerts = new List<Alert>();
        private Guid patientId;
        private Guid? organizationId;
        private bool isPatientSelected = false;
        private Guid activeUserOrganizationId { get; set; }

        private bool Subscription = false;
        private DialogOptions dialogOptions = new DialogOptions
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true,
            CloseOnEscapeKey = true
        };

        protected override async Task OnInitializedAsync()
        {
            try
            {
                UserLicense activeUserLicense = null; // Declare once at the top

                // Check if patient is selected
                if (PatientService.PatientData != null && PatientService.PatientData.Id != Guid.Empty)
                {
                    // Patient is selected - use patient-specific alerts
                    patientId = PatientService.PatientData.Id;
                    organizationId = PatientService.PatientData.OrganizationID;
                    isPatientSelected = true;

                    // Get user license for patient's organization
                    activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(organizationId);

                    await LoadAlertsForPatient();
                }
                else
                {
                    // Get organization by name and then get organization ID
                    var organization = await OrganizationService.GetOrganizationsByNameAsync(User.OrganizationName);
                    if (organization != null && organization.Count > 0)
                    {
                        activeUserOrganizationId = organization[0].OrganizationId;
                        organizationId = activeUserOrganizationId;

                        // Get user license for organization
                        activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(organization[0].OrganizationId);
                    }
                    else
                    {
                        Logger.LogWarning($"No organization found with name: {User.OrganizationName}");
                        organizationId = null;
                    }

                    isPatientSelected = false;
                    await LoadAlertsForOrganization();
                }

                // Get plan type if we have a license
                if (activeUserLicense != null)
                {
                    var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                    Subscription = planType.PlanName == Localizer["Enterprise"];
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error initializing Alerts component");
                Snackbar.Add("Error loading alerts. Please try again later.", Severity.Error);
                alerts = new List<Alert>();
            }
        }

        private async Task LoadAlertsForPatient()
        {
            try
            {
                var loadedAlerts = await AlertService.GetAllByIdAndIsActiveAsync(patientId, organizationId, false);
                if (loadedAlerts != null)
                {
                    // Ensure patient name is properly set
                    string currentPatientName = PatientService.PatientData?.Name ?? "Unknown";

                    foreach (var alert in loadedAlerts)
                    {
                        // If PatientName is showing "string" or is empty, fix it
                        if (string.IsNullOrWhiteSpace(alert.PatientName) ||
                            alert.PatientName.Equals("string", StringComparison.OrdinalIgnoreCase))
                        {
                            alert.PatientName = currentPatientName;
                        }
                    }

                    alerts = loadedAlerts;
                }
                else
                {
                    alerts = new List<Alert>();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading alerts for patient");
                Snackbar.Add("Error loading patient alerts from the server. Please try again later.", Severity.Error);
                alerts = new List<Alert>();
            }
        }

        private async Task LoadAlertsForOrganization()
        {
            try
            {
                if (organizationId.HasValue)
                {
                    var loadedAlerts = await AlertService.GetActiveAlertsByOrganizationIdAsync(organizationId.Value, organizationId, false);
                    if (loadedAlerts != null)
                    {
                        // Fix patient names using same logic as patient alerts
                        foreach (var alert in loadedAlerts)
                        {
                            // If PatientName is showing "string" or is empty, fix it
                            if (string.IsNullOrWhiteSpace(alert.PatientName) ||
                                alert.PatientName.Equals("string", StringComparison.OrdinalIgnoreCase))
                            {
                                // Try to get patient name from current patient data if it matches
                                if (PatientService?.PatientData != null && PatientService.PatientData.Id == alert.PatientId)
                                {
                                    alert.PatientName = PatientService.PatientData.Name ?? "Unknown Patient";
                                }
                                else
                                {
                                    alert.PatientName = "Patient"; // Fallback for other patients in the organization
                                }
                            }
                        }

                        alerts = loadedAlerts;
                    }
                    else
                    {
                        alerts = new List<Alert>();
                    }
                }
                else
                {
                    Logger.LogWarning("Organization ID is null, cannot load alerts");
                    alerts = new List<Alert>();
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading alerts for organization");
                Snackbar.Add("Error loading organization alerts from the server. Please try again later.", Severity.Error);
                alerts = new List<Alert>();
            }
        }

        public void OpenCustomAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomLabAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        public void OpenCustomImmunizationAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomImmunizationAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        /// <summary>
        /// Custom Alert Dialog Box For Storing Custom Procedure Alerts
        /// </summary>
        public void OpenCustomProcedureAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<CustomProcedureAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        public void OpenDIAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<DIAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        public void OpenDXAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<DXAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        public void OpenPatientSpecificAlertDialogAsync()
        {
            try
            {
                var options = new DialogOptions
                {
                    MaxWidth = MaxWidth.ExtraLarge,
                    FullWidth = true,
                    CloseOnEscapeKey = true,
                    CloseButton = true
                };

                DialogService.Show<PatientSpecificAlert>("", options);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorOpeningDialog"]);
            }
        }

        private IEnumerable<Alert> FilteredAlerts => alerts
            .Where(x => (string.IsNullOrWhiteSpace(searchString) ||
                         x.PatientName.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                         x.AlertType.Contains(searchString, StringComparison.OrdinalIgnoreCase) ||
                         x.Description.Contains(searchString, StringComparison.OrdinalIgnoreCase)))
            .Where(x => selectedSeverity == "All" || x.Severity == selectedSeverity)
            .ToList();

        private async Task DismissAlert(Alert alert)
        {
            try
            {
                // Show confirmation dialog
                bool? result = await DialogService.ShowMessageBox(
                Localizer["Confirm Alert Deletion"], // Title
                Localizer["Are you sure you want to delete this alert? This action cannot be undone."], // Message
                yesText: Localizer["Delete"],
                cancelText: Localizer["Cancel"]
                );

                // Check if user clicked OK (result will be true if OK is clicked, null if Cancel is clicked)
                if (result == true)
                {
                    alert.IsActive = false;
                    // Use appropriate organization ID based on context
                    var orgId = isPatientSelected ? organizationId : alert.OrganizationId;

                    await AlertService.UpdateAlertAsync(alert, orgId, false);
                    alerts.Remove(alert);

                    Snackbar.Add($"Alert for {alert.PatientName} has been deleted", Severity.Success);
                    dialogVisible = false;
                }
                // If result is null (Cancel clicked) or false, do nothing - alert won't be dismissed
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDismissingAlert"]);
                Snackbar.Add("Error dismissing alert. Please try again.", Severity.Error);
            }
        }

        // Method to refresh alerts when patient selection changes
        public async Task RefreshAlertsAsync()
        {
            await OnInitializedAsync();
        }
        private async Task ViewOrderSet(Alert alert)
        {
            try
            {
                var OrdersetId = alert.OrdersetId ?? Guid.Empty;
                if (OrdersetId != Guid.Empty)
                {
                    var allOrderSets = await orderSetService.GetAllOrderSetAsync();

                    var completeOrderSet = allOrderSets
                        .FirstOrDefault(o => o.orderSet != null && o.orderSet.Id == OrdersetId);
                    if (completeOrderSet != null)
                    {

                        var immunizationList = completeOrderSet.orderSetImmunizationData?
                            .Select(x => new ImmunizationData
                            {
                                ImmunizationId = Guid.NewGuid(),
                                PatientId = patientId,
                                OrganizationId = organizationId ?? Guid.Empty,
                                CreatedBy = Guid.Parse(User.id),
                                UpdatedBy = Guid.Parse(User.id),
                                CreatedDate = DateTime.UtcNow,
                                UpdatedDate = DateTime.UtcNow,
                                Immunizations = x.Immunizations,
                                CPTCode = x.CPTCode,
                                CVXCode = x.CVXCode,
                                Comments = x.Comments,
                                CPTDescription = x.CPTDescription,
                                GivenDate = x.GivenDate,
                                PCPId = Guid.Parse(User.id),
                                IsActive = true,
                                Subscription = Subscription
                            })
                            .ToList();
                        if (immunizationList?.Any() == true)
                            await _ImmunizationService.AddImmunizationAsync(immunizationList, organizationId, Subscription);

                        var procedureList = completeOrderSet.orderSetProcedures?
                            .Select(x => new Procedures
                            {
                                Id = Guid.NewGuid(),
                                PatientId = patientId,
                                OrganizationId = organizationId ?? Guid.Empty,
                                CreatedByUserId = Guid.Parse(User.id),
                                UpdatedByUserId = Guid.Parse(User.id),
                                OrderDate = DateTime.UtcNow,
                                LastUpdatedDate = DateTime.UtcNow,
                                CPTCode = x.CPTCode,
                                Notes = x.Notes,
                                Description = x.Description,
                                AssessmentData = x.AssessmentData,
                                AssessmentId = x.AssessmentId ?? Guid.Empty,
                                ChiefComplaint = x.ChiefComplaint,
                                ChiefComplaintId = x.ChiefComplaintId,
                                PcpId = Guid.Parse(User.id),
                                OrderedBy = User.givenName + " " + User.surname,
                                IsDeleted = true,
                                Subscription = Subscription
                            })
                            .ToList();
                        if (procedureList?.Any() == true)
                            await ProcedureService.AddProcedureAsync(procedureList, organizationId, Subscription);

                        var diagnosticImaging = completeOrderSet.orderSetDiagnosticImaging;

                        var DiRecord = new DiagnosticImage
                        {
                            RecordID = Guid.NewGuid(),
                            PatientId = patientId,
                            OrganizationID = organizationId,
                            CreatedBy = Guid.Parse(User.id),
                            UpdatedBy = Guid.Parse(User.id),
                            CreatedDate = DateTime.UtcNow,
                            UpdatedDate = DateTime.UtcNow,
                            DiCompany = diagnosticImaging.DiCompany,
                            ccResults = diagnosticImaging.ccResults,
                            Type = diagnosticImaging.Type,
                            Lookup = diagnosticImaging.Lookup,
                            OrderName = diagnosticImaging.OrderName,
                            StartsWith = diagnosticImaging.StartsWith,
                            Subscription = Subscription,
                            IsActive = true,
                        };
                        await DiagnosticImagingService.CreateDiagnosticImagingAsync(new List<DiagnosticImage> { DiRecord }, organizationId, Subscription);

                        var labsList = completeOrderSet.orderSetLabTests?
                        .Select(x => new LabTests
                        {
                            LabTestsId = Guid.NewGuid(),
                            PatientId = patientId,
                            OrganizationId = organizationId ?? Guid.Empty,
                            PcpId = Guid.Parse(User.id),
                            CreatedDate = DateTime.UtcNow,
                            UpdatedDate = DateTime.UtcNow,
                            LabTest1 = x.LabTest1,
                            LabTest2 = x.LabTest2,
                            TestOrganization = x.TestOrganization,
                            AssessmentData = x.AssessmentData,
                            AssessmentId =x.AssessmentId ?? Guid.Empty,
                            IsActive = true,
                        })
                        .ToList();
                        if (labsList?.Any() == true)
                            await _labTestsService.AddLabTestsAsync(labsList);

                        var medicationList = completeOrderSet.orderSetActiveMedication?
                        .Select(x => new ActiveMedication
                        {
                            MedicineId = Guid.NewGuid(),
                            PatientId = patientId,
                            OrganizationId = organizationId ?? Guid.Empty,
                            CreatedBy = Guid.Parse(User.id),
                            UpdatedBy = Guid.Parse(User.id),
                            CreatedDate = DateTime.UtcNow,
                            UpdatedDate = DateTime.UtcNow,
                            BrandName = x.BrandName,
                            DrugDetails = x.DrugDetails,
                            Quantity = x.Quantity,
                            Frequency = x.Frequency,
                            Route = x.Route,
                            Take = x.Take,
                            PCPId = Guid.Parse(User.id),
                            Strength = x.Strength,
                            StartDate = x.StartDate,
                            EndDate = x.EndDate,
                            isActive = true,
                            CheifComplaint = x.CheifComplaint,
                            CheifComplaintId = x.CheifComplaintId ?? Guid.Empty,
                            Subscription = Subscription
                        })
                        .ToList();
                        if (medicationList?.Any() == true)
                            await CurrentMedicationService.AddMedicationAsync(medicationList, organizationId, Subscription);
                    }
                    else
                    {
                        Snackbar.Add(Localizer["RecordNotFound"], Severity.Error);
                    }
                }
         
                await OnSwitchToNotes.InvokeAsync();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, Localizer["ErrorDismissingAlert"]);
            }
        }
    }
}