﻿using Markdig;
using Microsoft.AspNetCore.Components;
using Microsoft.Graph.Models;
using MudBlazor;
using Syncfusion.Blazor.Grids;
using Syncfusion.Blazor.RichTextEditor;
using Syncfusion.Windows.Forms.Grid;
using Syncfusion.Windows.Shared.Resources;
using System.Reflection;
using System.Text;
using System.Text.Json;
using TeyaUIModels.Model;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Components.Layout;
using TeyaWebApp.Services;
using TeyaWebApp.ViewModel;
using static TeyaWebApp.Components.Pages.Encounters;

namespace TeyaWebApp.Components.Pages.Billing
{
    public partial class Encounters : ComponentBase
    {

        private List<Record> Records;
        private List<Member> AllMembers;
        private Guid OrgID { get; set; }
        private bool Subscription { get; set; }
        private Guid? PatientID { get; set; }
        private bool IsReadOnly { get; set; } = true;
        private List<Member> ProviderMembers { get; set; }
        private string patientFilter = string.Empty;
        private string providerFilter = string.Empty;
        [Inject] private ActiveUser User { get; set; }
        private DateTime? dateFromFilter;
        private DateTime? dateToFilter;
        private bool? isEditableFilter = null;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                OrgID = await OrganizationService.GetOrganizationIdByNameAsync(User.OrganizationName);
                var activeUserLicense = await UserLicenseService.GetUserLicenseByOrganizationIdAsync(OrgID);
                var planType = await PlanTypeService.GetPlanTypeByIdAsync(activeUserLicense.PlanId);
                Subscription = planType.PlanName == "Enterprise";
                AllMembers = await MemberService.GetAllMembersAsync(OrgID, Subscription);
                ProviderMembers = AllMembers.Where(m => m.RoleName == "Provider").ToList();
                Records = await ProgressNotesService.GetAllByOrgIdAsync(OrgID, Subscription);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing Encounters: {ex.Message}");
            }
        }
        private List<Record> filteredEncounters => Records?
            .Where(r => string.IsNullOrEmpty(patientFilter) ||
                    (r.PatientName?.Contains(patientFilter, StringComparison.OrdinalIgnoreCase) ?? false))
            .Where(r =>
        // Provider name filter (new)
        (string.IsNullOrEmpty(providerFilter) ||
        ProviderMembers.Any(p =>
            p.Id == r.PCPId &&
            (p.FirstName?.StartsWith(providerFilter, StringComparison.OrdinalIgnoreCase) == true ||
            p.LastName?.StartsWith(providerFilter, StringComparison.OrdinalIgnoreCase) == true ||
            p.UserName?.StartsWith(providerFilter, StringComparison.OrdinalIgnoreCase) == true))))
            .Where(r =>
                (!dateFromFilter.HasValue ||
                (r.DateTime >= dateFromFilter.Value)))
            .Where(r =>
                (!dateToFilter.HasValue ||
                (r.DateTime <= dateToFilter.Value)))
            .Where(r =>
            (!isEditableFilter.HasValue ||
            r.isEditable == isEditableFilter.Value))
            .ToList() ?? new List<Record>();

        private void ResetFilters()
        {
            patientFilter = string.Empty;
            providerFilter = string.Empty;
            isEditableFilter = null;
            dateFromFilter = null;
            dateToFilter = null;
        }
    }
}

